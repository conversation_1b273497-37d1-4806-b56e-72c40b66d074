# 🏗️ DIAGRAMME DE CLASSES - CEREBLOOM CLASSIFY

## 📋 **ARCHITECTURE GLOBALE**

```mermaid
classDiagram
    %% ===== FRONTEND REACT/TYPESCRIPT =====

    class App {
        +QueryClientProvider
        +AuthProvider
        +ThemeProvider
        +BrowserRouter
        +render() ReactElement
    }

    class AuthContext {
        +currentUser: User
        +userData: UserData
        +login(email, password) Promise~void~
        +logout() Promise~void~
        +register(email, password, name, role) Promise~void~
        +loading: boolean
    }

    class ThemeContext {
        +theme: Theme
        +setTheme(theme) void
    }

    class ClassificationTool {
        +file: File
        +isProcessing: boolean
        +result: ClassificationResult
        +handleFileSelect(file) void
        +handleClassification(file) Promise~void~
        +handleReset() void
    }

    class UploadArea {
        +onFileSelect: Function
        +isLoading: boolean
        +isDragging: boolean
        +preview: string
        +error: string
        +handleDragOver(event) void
        +handleDrop(event) void
        +validateFile(file) boolean
    }

    class ResultView {
        +result: ClassificationResult
        +onReset: Function
        +activeTab: string
        +getConfidenceLevel(confidence) Object
        +render() ReactElement
    }

    class Dashboard {
        +stats: DashboardStats
        +recentScans: Scan[]
        +patients: Patient[]
        +loadStats() Promise~void~
        +render() ReactElement
    }

    class Patients {
        +patients: Patient[]
        +searchQuery: string
        +filteredPatients: Patient[]
        +handleSearch(query) void
        +handleEdit(patient) void
        +handleDelete(id) void
    }

    %% ===== INTERFACES TYPESCRIPT =====

    class ClassificationResult {
        <<interface>>
        +tumorType: string
        +confidence: number
        +description: string
        +recommendations: string[]
        +imageUrl: string
        +processingTime: string
        +model: string
        +resolution: string
        +scanType: string
        +size?: string
        +location?: string
        +density?: string
        +borders?: string
    }

    class Patient {
        <<interface>>
        +id: string
        +firstName: string
        +lastName: string
        +dateOfBirth: string
        +gender: Gender
        +contactNumber: string
        +email: string
        +address: string
        +bloodType: BloodType
        +height: number
        +weight: number
        +emergencyContact: EmergencyContact
        +insurance: Insurance
        +doctor: string
        +medicalHistory: MedicalHistory
        +lastScan?: string
        +lastVisit?: string
        +nextAppointment?: string
        +notes: string
    }

    class Scan {
        <<interface>>
        +id: string
        +patientId: string
        +date: string
        +type: ScanType
        +bodyPart: string
        +imageUrl: string
        +result: ScanResult
        +doctor: string
        +facility: string
        +status: ScanStatus
    }

    class Treatment {
        <<interface>>
        +id: string
        +patientId: string
        +type: TreatmentType
        +name: string
        +startDate: string
        +endDate?: string
        +frequency?: string
        +dosage?: string
        +doctor: string
        +notes: string
        +status: TreatmentStatus
        +sideEffects: string[]
        +effectiveness: string
    }

    class UserData {
        <<interface>>
        +uid: string
        +email: string
        +displayName: string
        +role: UserRole
        +createdAt?: string
    }

    %% ===== ENUMS TYPESCRIPT =====

    class UserRole {
        <<enumeration>>
        ADMIN
        DOCTOR
    }

    class ScanType {
        <<enumeration>>
        MRI
        CT
        PET
        XRAY
    }

    class ScanStatus {
        <<enumeration>>
        COMPLETED
        PENDING
        PROCESSING
        FAILED
    }

    class TreatmentType {
        <<enumeration>>
        MEDICATION
        SURGERY
        RADIATION
        CHEMOTHERAPY
        PHYSICAL_THERAPY
        OTHER
    }

    class Gender {
        <<enumeration>>
        MALE
        FEMALE
        OTHER
    }

    class BloodType {
        <<enumeration>>
        A_POSITIVE
        A_NEGATIVE
        B_POSITIVE
        B_NEGATIVE
        AB_POSITIVE
        AB_NEGATIVE
        O_POSITIVE
        O_NEGATIVE
    }

    %% ===== RELATIONS FRONTEND =====
    App --> AuthContext
    App --> ThemeContext
    App --> ClassificationTool
    App --> Dashboard
    App --> Patients

    ClassificationTool --> UploadArea
    ClassificationTool --> ResultView
    ClassificationTool --> ClassificationResult

    ResultView --> ClassificationResult

    Dashboard --> Patient
    Dashboard --> Scan

    Patients --> Patient

    Patient --> Gender
    Patient --> BloodType
    Patient --> UserRole

    Scan --> ScanType
    Scan --> ScanStatus

    Treatment --> TreatmentType

    UserData --> UserRole
```

## 📋 **BACKEND PYTHON/FASTAPI**

```mermaid
classDiagram
    %% ===== APPLICATION FASTAPI =====

    class FastAPIApp {
        +title: string
        +description: string
        +version: string
        +docs_url: string
        +redoc_url: string
        +middleware: CORSMiddleware
        +security: HTTPBearer
        +get_current_user(credentials) UserResponse
        +root() dict
        +health_check() dict
        +classify_image(file, user) ClassificationResult
        +segment_image(file, user) SegmentationResult
        +login(data) TokenResponse
        +register(data) UserResponse
        +get_patients(user) List~PatientResponse~
        +create_patient(data, user) PatientResponse
    }

    %% ===== SERVICES BACKEND =====

    class ClassificationService {
        +mock_results: List~dict~
        +model_loaded: boolean
        +__init__()
        +check_model_health() dict
        +classify_image(path, filename) ClassificationResult
        +_simulate_processing_time() void
        +_validate_image(path) void
        +get_supported_formats() List~string~
        +get_model_info() dict
    }

    class SegmentationService {
        +model: TensorFlowModel
        +model_loaded: boolean
        +segment_classes: dict
        +colors: List~tuple~
        +__init__()
        +check_model_health() dict
        +segment_image(path, filename) SegmentationResult
        +_load_model() void
        +_simulate_segmentation(path, filename, time) SegmentationResult
        +_process_nifti_file(path, filename, time) SegmentationResult
        +_load_and_preprocess_nifti(path) ndarray
        +_predict_segmentation(data) ndarray
        +_find_optimal_slice(predictions) int
        +_generate_result_images(predictions, slice, filename) dict
        +_calculate_metrics(predictions) dict
    }

    class AuthService {
        +pwd_context: CryptContext
        +users_db_path: string
        +users_db: dict
        +__init__()
        +_load_users_db() dict
        +_save_users_db() void
        +_create_default_admin() void
        +_verify_password(plain, hashed) boolean
        +_get_password_hash(password) string
        +_create_access_token(data, expires) string
        +verify_token(token) UserResponse
        +authenticate_user(email, password) TokenResponse
        +create_user(data) UserResponse
        +get_user_by_email(email) UserResponse
        +update_user(email, data) UserResponse
        +delete_user(email) boolean
        +get_all_users() List~UserResponse~
    }

    class PatientService {
        +patients_db_path: string
        +scans_db_path: string
        +treatments_db_path: string
        +appointments_db_path: string
        +patients_db: dict
        +scans_db: dict
        +treatments_db: dict
        +appointments_db: dict
        +__init__()
        +_load_db(path, default) dict
        +_save_db(path, data) void
        +_create_mock_data() void
        +create_patient(data) PatientResponse
        +get_patient(id) PatientResponse
        +get_all_patients() List~PatientResponse~
        +update_patient(id, data) PatientResponse
        +delete_patient(id) boolean
        +create_scan(data) ScanResponse
        +get_patient_scans(patient_id) List~ScanResponse~
        +update_scan_result(id, data) ScanResponse
        +get_dashboard_stats() dict
    }

    %% ===== MODÈLES PYDANTIC =====

    class LoginRequest {
        <<BaseModel>>
        +email: EmailStr
        +password: string
    }

    class TokenResponse {
        <<BaseModel>>
        +access_token: string
        +token_type: string
        +expires_in: int
        +user: UserResponse
    }

    class UserCreate {
        <<BaseModel>>
        +email: EmailStr
        +password: string
        +displayName: string
        +role: UserRole
    }

    class UserResponse {
        <<BaseModel>>
        +uid: string
        +email: string
        +displayName: string
        +role: UserRole
        +createdAt: datetime
    }

    class ClassificationResultPydantic {
        <<BaseModel>>
        +tumorType: string
        +confidence: float
        +description: string
        +recommendations: List~string~
        +imageUrl: string
        +processingTime: string
        +model: string
        +resolution: string
        +scanType: string
        +size: string
        +location: string
        +density: string
        +borders: string
    }

    class SegmentationResultPydantic {
        <<BaseModel>>
        +originalImageUrl: string
        +segmentedImageUrl: string
        +overlayImageUrl: string
        +optimalSlice: int
        +segmentClasses: dict
        +processingTime: string
        +confidence: float
        +diceCoefficient: float
        +precision: float
        +recall: float
        +specificity: float
    }

    class PatientCreate {
        <<BaseModel>>
        +firstName: string
        +lastName: string
        +dateOfBirth: string
        +gender: Gender
        +contactNumber: string
        +email: EmailStr
        +address: string
        +bloodType: BloodType
        +height: int
        +weight: int
        +emergencyContact: EmergencyContact
        +insurance: Insurance
        +doctor: string
        +medicalHistory: MedicalHistory
        +notes: string
    }

    class PatientResponse {
        <<BaseModel>>
        +id: string
        +firstName: string
        +lastName: string
        +dateOfBirth: string
        +gender: Gender
        +contactNumber: string
        +email: EmailStr
        +address: string
        +bloodType: BloodType
        +height: int
        +weight: int
        +emergencyContact: EmergencyContact
        +insurance: Insurance
        +doctor: string
        +medicalHistory: MedicalHistory
        +lastScan: string
        +lastVisit: string
        +nextAppointment: string
        +notes: string
    }

    class ScanCreate {
        <<BaseModel>>
        +patientId: string
        +type: ScanType
        +bodyPart: string
        +doctor: string
        +facility: string
        +notes: string
    }

    class ScanResponse {
        <<BaseModel>>
        +id: string
        +patientId: string
        +date: string
        +type: ScanType
        +bodyPart: string
        +imageUrl: string
        +result: ScanResult
        +doctor: string
        +facility: string
        +status: ScanStatus
    }

    %% ===== ENUMS PYDANTIC =====

    class UserRolePydantic {
        <<Enum>>
        admin
        doctor
    }

    class ScanTypePydantic {
        <<Enum>>
        MRI
        CT
        PET
        XRAY
    }

    class ScanStatusPydantic {
        <<Enum>>
        completed
        pending
        processing
        failed
    }

    class TreatmentTypePydantic {
        <<Enum>>
        medication
        surgery
        radiation
        chemotherapy
        physical_therapy
        other
    }

    class GenderPydantic {
        <<Enum>>
        male
        female
        other
    }

    class BloodTypePydantic {
        <<Enum>>
        A_POSITIVE
        A_NEGATIVE
        B_POSITIVE
        B_NEGATIVE
        AB_POSITIVE
        AB_NEGATIVE
        O_POSITIVE
        O_NEGATIVE
    }

    %% ===== UTILITAIRES =====

    class FileUtils {
        +save_upload_file(file) string
        +validate_image_file(file) boolean
        +validate_image_content(path) tuple
        +get_image_info(path) dict
        +cleanup_temp_files(dir, age) void
        +create_thumbnail(path, size) string
        +get_supported_formats() List~string~
        +format_file_size(bytes) string
        +ensure_directory_exists(path) void
    }

    class Settings {
        +API_V1_STR: string
        +PROJECT_NAME: string
        +VERSION: string
        +BACKEND_CORS_ORIGINS: List~string~
        +SECRET_KEY: string
        +ALGORITHM: string
        +ACCESS_TOKEN_EXPIRE_MINUTES: int
        +UPLOAD_DIR: string
        +RESULTS_DIR: string
        +MAX_FILE_SIZE: int
        +ALLOWED_EXTENSIONS: List~string~
        +MODEL_PATH: string
        +IMG_SIZE: int
        +VOLUME_SLICES: int
        +DATABASE_URL: string
        +LOG_LEVEL: string
    }

    %% ===== RELATIONS BACKEND =====
    FastAPIApp --> ClassificationService
    FastAPIApp --> SegmentationService
    FastAPIApp --> AuthService
    FastAPIApp --> PatientService
    FastAPIApp --> Settings

    ClassificationService --> ClassificationResultPydantic
    ClassificationService --> FileUtils

    SegmentationService --> SegmentationResultPydantic
    SegmentationService --> FileUtils

    AuthService --> UserCreate
    AuthService --> UserResponse
    AuthService --> TokenResponse
    AuthService --> LoginRequest
    AuthService --> UserRolePydantic

    PatientService --> PatientCreate
    PatientService --> PatientResponse
    PatientService --> ScanCreate
    PatientService --> ScanResponse

    PatientCreate --> GenderPydantic
    PatientCreate --> BloodTypePydantic
    PatientResponse --> GenderPydantic
    PatientResponse --> BloodTypePydantic

    ScanCreate --> ScanTypePydantic
    ScanResponse --> ScanTypePydantic
    ScanResponse --> ScanStatusPydantic

    UserCreate --> UserRolePydantic
    UserResponse --> UserRolePydantic
```

## 🔗 **ARCHITECTURE COMPLÈTE - VUE D'ENSEMBLE**

```mermaid
classDiagram
    %% ===== COUCHE PRÉSENTATION (FRONTEND) =====

    class FrontendLayer {
        <<layer>>
        +React Components
        +TypeScript Interfaces
        +Context Providers
        +UI Components
    }

    class APILayer {
        <<layer>>
        +HTTP Requests
        +Authentication
        +Error Handling
        +Data Transformation
    }

    %% ===== COUCHE MÉTIER (BACKEND) =====

    class BusinessLayer {
        <<layer>>
        +Classification Service
        +Segmentation Service
        +Patient Management
        +Authentication Logic
    }

    class DataLayer {
        <<layer>>
        +JSON Storage
        +File Management
        +Model Storage
        +Configuration
    }

    %% ===== COUCHE IA =====

    class AILayer {
        <<layer>>
        +TensorFlow Models
        +Image Processing
        +U-Net Segmentation
        +Classification Algorithms
    }

    %% ===== RELATIONS ARCHITECTURALES =====
    FrontendLayer --> APILayer : HTTP/REST
    APILayer --> BusinessLayer : FastAPI
    BusinessLayer --> DataLayer : JSON/Files
    BusinessLayer --> AILayer : Model Inference

    %% ===== FLUX DE DONNÉES =====

    class DataFlow {
        +User Upload Image
        +Frontend Validation
        +API Authentication
        +Backend Processing
        +AI Classification
        +Result Storage
        +Response to Frontend
        +UI Update
    }
```

## 📊 **STATISTIQUES DU PROJET**

### **Frontend React/TypeScript**
- **Composants React** : ~25 composants
- **Interfaces TypeScript** : ~15 interfaces principales
- **Contextes** : 2 (Auth, Theme)
- **Pages** : 8 pages principales
- **Services** : 3 services utilitaires

### **Backend FastAPI/Python**
- **Services** : 4 services métier
- **Modèles Pydantic** : ~20 schémas
- **Endpoints** : ~15 endpoints REST
- **Utilitaires** : 1 module d'utilitaires
- **Configuration** : 1 module de configuration

### **Modèles de Données**
- **Entités principales** : Patient, Scan, Treatment, User
- **Enums** : 6 énumérations
- **Relations** : Hiérarchie patient → scans → traitements

## 🎯 **PATTERNS ARCHITECTURAUX UTILISÉS**

### **Frontend**
- **Component Pattern** : Composants React réutilisables
- **Context Pattern** : Gestion d'état global (Auth, Theme)
- **Hook Pattern** : Logique métier dans des hooks personnalisés
- **Provider Pattern** : Injection de dépendances via contextes

### **Backend**
- **Service Layer Pattern** : Séparation logique métier/présentation
- **Repository Pattern** : Abstraction de l'accès aux données
- **Dependency Injection** : Services injectés dans FastAPI
- **Strategy Pattern** : Différentes stratégies de classification

### **Communication**
- **REST API** : Communication HTTP standardisée
- **JWT Authentication** : Authentification stateless
- **CORS** : Communication cross-origin sécurisée
- **Request/Response Pattern** : Échange de données structurées

## 🔐 **SÉCURITÉ**

### **Authentification**
- **JWT Tokens** : Authentification stateless
- **Password Hashing** : Bcrypt pour les mots de passe
- **Role-Based Access** : Contrôle d'accès par rôles
- **Token Expiration** : Expiration automatique des tokens

### **Validation**
- **Pydantic Validation** : Validation automatique des données
- **File Validation** : Vérification des types et tailles de fichiers
- **Input Sanitization** : Nettoyage des entrées utilisateur
- **CORS Configuration** : Contrôle des origines autorisées

## 🚀 **PERFORMANCE**

### **Frontend**
- **Lazy Loading** : Chargement différé des composants
- **Code Splitting** : Division du code en chunks
- **Memoization** : Optimisation des re-rendus React
- **Asset Optimization** : Compression des assets

### **Backend**
- **Async/Await** : Traitement asynchrone
- **Connection Pooling** : Réutilisation des connexions
- **Caching Strategy** : Cache des résultats fréquents
- **File Streaming** : Traitement en flux des gros fichiers

## 📈 **ÉVOLUTIVITÉ**

### **Modularité**
- **Services découplés** : Indépendance des modules
- **Interfaces standardisées** : Contrats d'API stables
- **Configuration externalisée** : Paramètres modifiables
- **Plugin Architecture** : Extensibilité via plugins

### **Scalabilité**
- **Microservices Ready** : Architecture préparée pour la distribution
- **Database Abstraction** : Migration facile vers PostgreSQL/MongoDB
- **Load Balancing Ready** : Support de la répartition de charge
- **Container Ready** : Dockerisation possible

---

**🎯 Ce diagramme de classes couvre l'intégralité de l'application CereBloom Classify, du frontend React/TypeScript au backend FastAPI/Python, en passant par les modèles de données, les services métier et l'architecture IA.**

"""
CereBloom Classify - Backend API
FastAPI backend pour la classification et segmentation de tumeurs cérébrales
Compatible avec le frontend React TypeScript
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
import uvicorn
import os
import logging
from datetime import datetime
from typing import List, Optional
import json

# Import des modules locaux
from models.schemas import (
    ClassificationResult,
    SegmentationResult,
    UserCreate,
    UserResponse,
    LoginRequest,
    TokenResponse,
    PatientCreate,
    PatientResponse,
    ScanCreate,
    ScanResponse
)
from services.classification_service import ClassificationService
from services.segmentation_service import SegmentationService
from services.auth_service import AuthService
from services.patient_service import PatientService
from mlops.mlops_manager import MLOpsManager
from utils.file_utils import save_upload_file, validate_image_file
from config import settings

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialisation de l'application FastAPI
app = FastAPI(
    title="CereBloom Classify API",
    description="API pour la classification et segmentation de tumeurs cérébrales",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configuration CORS pour le frontend React
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialisation des services
classification_service = ClassificationService()
segmentation_service = SegmentationService()
auth_service = AuthService()
patient_service = PatientService()
mlops_manager = MLOpsManager()

# Configuration des templates
templates = Jinja2Templates(directory="templates")

# Security
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Vérification du token JWT pour l'authentification"""
    try:
        user = await auth_service.verify_token(credentials.credentials)
        return user
    except Exception as e:
        logger.error(f"Erreur d'authentification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token invalide",
            headers={"WWW-Authenticate": "Bearer"},
        )

@app.get("/")
async def root():
    """Endpoint racine pour vérifier que l'API fonctionne"""
    return {
        "message": "CereBloom Classify API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Endpoint de vérification de santé de l'API"""
    try:
        # Vérifier que le modèle est chargé
        model_status = classification_service.check_model_health()
        segmentation_status = segmentation_service.check_model_health()

        return {
            "status": "healthy",
            "classification_model": model_status,
            "segmentation_model": segmentation_status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erreur lors du health check: {e}")
        raise HTTPException(status_code=500, detail="Service non disponible")

# ==================== ENDPOINTS D'AUTHENTIFICATION ====================

@app.post("/auth/login", response_model=TokenResponse)
async def login(login_data: LoginRequest):
    """Connexion utilisateur"""
    try:
        result = await auth_service.authenticate_user(login_data.email, login_data.password)
        logger.info(f"Connexion réussie pour l'utilisateur: {login_data.email}")
        return result
    except Exception as e:
        logger.error(f"Erreur de connexion pour {login_data.email}: {e}")
        raise HTTPException(status_code=401, detail="Email ou mot de passe incorrect")

@app.post("/auth/register", response_model=UserResponse)
async def register(user_data: UserCreate):
    """Inscription d'un nouvel utilisateur"""
    try:
        user = await auth_service.create_user(user_data)
        logger.info(f"Nouvel utilisateur créé: {user.email}")
        return user
    except Exception as e:
        logger.error(f"Erreur lors de l'inscription: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user = Depends(get_current_user)):
    """Obtenir les informations de l'utilisateur connecté"""
    return current_user

# ==================== ENDPOINTS DE CLASSIFICATION ====================

@app.post("/classify", response_model=ClassificationResult)
async def classify_image(
    file: UploadFile = File(...),
    current_user = Depends(get_current_user)
):
    """
    Classification d'une image IRM
    Compatible avec le frontend ClassificationTool.tsx
    """
    try:
        # Validation du fichier
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="Format de fichier non supporté")

        # Sauvegarde temporaire du fichier
        file_path = await save_upload_file(file)

        logger.info(f"Classification demandée par {current_user.email} pour le fichier: {file.filename}")

        # Classification de l'image
        result = await classification_service.classify_image(file_path, file.filename)

        # Nettoyage du fichier temporaire
        if os.path.exists(file_path):
            os.remove(file_path)

        logger.info(f"Classification terminée: {result.tumorType} avec {result.confidence}% de confiance")
        return result

    except Exception as e:
        logger.error(f"Erreur lors de la classification: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la classification: {str(e)}")

@app.post("/segment", response_model=SegmentationResult)
async def segment_image(
    file: UploadFile = File(...),
    current_user = Depends(get_current_user)
):
    """
    Segmentation d'une image IRM avec le modèle U-Net
    """
    try:
        # Validation du fichier
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="Format de fichier non supporté")

        # Sauvegarde temporaire du fichier
        file_path = await save_upload_file(file)

        logger.info(f"Segmentation demandée par {current_user.email} pour le fichier: {file.filename}")

        # Segmentation de l'image
        result = await segmentation_service.segment_image(file_path, file.filename)

        # Nettoyage du fichier temporaire
        if os.path.exists(file_path):
            os.remove(file_path)

        logger.info(f"Segmentation terminée pour {file.filename}")
        return result

    except Exception as e:
        logger.error(f"Erreur lors de la segmentation: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la segmentation: {str(e)}")

# ==================== ENDPOINTS PATIENTS ====================

@app.post("/patients", response_model=PatientResponse)
async def create_patient(
    patient_data: PatientCreate,
    current_user = Depends(get_current_user)
):
    """Création d'un nouveau patient"""
    try:
        patient = await patient_service.create_patient(patient_data)
        logger.info(f"Patient créé par {current_user.email}: {patient.firstName} {patient.lastName}")
        return patient
    except Exception as e:
        logger.error(f"Erreur lors de la création du patient: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/patients", response_model=List[PatientResponse])
async def get_all_patients(current_user = Depends(get_current_user)):
    """Récupération de tous les patients"""
    try:
        patients = await patient_service.get_all_patients()
        return patients
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des patients: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/patients/{patient_id}", response_model=PatientResponse)
async def get_patient(
    patient_id: str,
    current_user = Depends(get_current_user)
):
    """Récupération d'un patient par ID"""
    try:
        patient = await patient_service.get_patient(patient_id)
        if not patient:
            raise HTTPException(status_code=404, detail="Patient non trouvé")
        return patient
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du patient {patient_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/patients/{patient_id}/scans", response_model=List[ScanResponse])
async def get_patient_scans(
    patient_id: str,
    current_user = Depends(get_current_user)
):
    """Récupération des scans d'un patient"""
    try:
        scans = await patient_service.get_patient_scans(patient_id)
        return scans
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des scans du patient {patient_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== ENDPOINTS DASHBOARD ====================

@app.get("/dashboard/stats")
async def get_dashboard_stats(current_user = Depends(get_current_user)):
    """Statistiques pour le dashboard"""
    try:
        stats = await patient_service.get_dashboard_stats()
        return stats
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des statistiques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== ENDPOINTS FICHIERS ====================

@app.get("/images/{filename}")
async def get_image(filename: str):
    """Récupération d'une image uploadée"""
    file_path = os.path.join(settings.UPLOAD_DIR, filename)
    if os.path.exists(file_path):
        return FileResponse(file_path)
    raise HTTPException(status_code=404, detail="Image non trouvée")

@app.get("/results/{filename}")
async def get_result_image(filename: str):
    """Récupération d'une image de résultat"""
    file_path = os.path.join(settings.RESULTS_DIR, filename)
    if os.path.exists(file_path):
        return FileResponse(file_path)
    raise HTTPException(status_code=404, detail="Image de résultat non trouvée")

# ==================== ENDPOINTS MLOPS ====================

@app.get("/mlops/health")
async def get_mlops_health(current_user = Depends(get_current_user)):
    """État de santé des modèles ML"""
    try:
        health_data = mlops_manager.get_model_health()
        return health_data
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de l'état MLOps: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mlops/dashboard")
async def get_mlops_dashboard(current_user = Depends(get_current_user)):
    """Dashboard MLOps complet"""
    try:
        dashboard_data = mlops_manager.get_dashboard_data()
        return dashboard_data
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du dashboard MLOps: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mlops/models")
async def list_models(current_user = Depends(get_current_user)):
    """Liste des modèles enregistrés"""
    try:
        models = mlops_manager.registry.list_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des modèles: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mlops/monitoring/metrics")
async def get_monitoring_metrics(
    hours: int = 24,
    current_user = Depends(get_current_user)
):
    """Métriques de monitoring"""
    try:
        metrics = mlops_manager.monitor.get_metrics_summary(hours=hours)
        return metrics
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des métriques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mlops/monitoring/alerts")
async def get_active_alerts(current_user = Depends(get_current_user)):
    """Alertes actives"""
    try:
        alerts = mlops_manager.monitor.get_active_alerts()
        return {"alerts": alerts}
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des alertes: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mlops/dashboard/web", response_class=HTMLResponse)
async def get_mlops_dashboard_web(request: Request):
    """Dashboard MLOps Web Interface"""
    try:
        return templates.TemplateResponse("mlops_dashboard.html", {"request": request})
    except Exception as e:
        logger.error(f"Erreur lors du chargement du dashboard web: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

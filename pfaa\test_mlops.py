#!/usr/bin/env python3
"""
Script de test MLOps pour CereBloom Classify
Teste toutes les fonctionnalités MLOps
"""

import os
import sys
import time
import requests
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration de l'API
API_BASE_URL = "http://localhost:8000"
TEST_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "admin123"
}

class MLOpsAPITester:
    """Testeur pour les APIs MLOps"""
    
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        
    def authenticate(self):
        """Authentification auprès de l'API"""
        try:
            logger.info("🔐 Authentification...")
            
            response = self.session.post(
                f"{API_BASE_URL}/auth/login",
                json=TEST_CREDENTIALS
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data["access_token"]
                self.session.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })
                logger.info("✅ Authentification réussie")
                return True
            else:
                logger.error(f"❌ Échec de l'authentification: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur d'authentification: {e}")
            return False
    
    def test_health_endpoint(self):
        """Test de l'endpoint de santé MLOps"""
        try:
            logger.info("🏥 Test de l'endpoint de santé MLOps...")
            
            response = self.session.get(f"{API_BASE_URL}/mlops/health")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ Endpoint de santé accessible")
                logger.info(f"   Status global: {data.get('overall_status', 'UNKNOWN')}")
                
                # Affichage des métriques de monitoring
                monitoring = data.get('monitoring', {})
                if monitoring:
                    logger.info(f"   Total prédictions: {monitoring.get('total_predictions', 0)}")
                    logger.info(f"   Taux d'erreur: {monitoring.get('error_rate_percent', 0):.1f}%")
                
                return True
            else:
                logger.error(f"❌ Échec du test de santé: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du test de santé: {e}")
            return False
    
    def test_dashboard_endpoint(self):
        """Test de l'endpoint dashboard MLOps"""
        try:
            logger.info("📊 Test du dashboard MLOps...")
            
            response = self.session.get(f"{API_BASE_URL}/mlops/dashboard")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ Dashboard MLOps accessible")
                
                # Affichage des statistiques des modèles
                models = data.get('models', {})
                if models:
                    logger.info(f"   Total modèles: {models.get('total', 0)}")
                    registry_stats = models.get('registry_stats', {})
                    if registry_stats:
                        logger.info(f"   Taille registre: {registry_stats.get('total_size_mb', 0):.1f} MB")
                
                return True
            else:
                logger.error(f"❌ Échec du test dashboard: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du test dashboard: {e}")
            return False
    
    def test_models_endpoint(self):
        """Test de l'endpoint des modèles"""
        try:
            logger.info("🤖 Test de l'endpoint des modèles...")
            
            response = self.session.get(f"{API_BASE_URL}/mlops/models")
            
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                logger.info(f"✅ Endpoint des modèles accessible ({len(models)} modèles)")
                
                for model in models[:3]:  # Affiche les 3 premiers modèles
                    logger.info(f"   • {model.get('model_name')} v{model.get('version')}")
                    logger.info(f"     Stage: {model.get('stage', 'none')}")
                    logger.info(f"     Status: {model.get('status', 'unknown')}")
                
                return True
            else:
                logger.error(f"❌ Échec du test des modèles: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du test des modèles: {e}")
            return False
    
    def test_monitoring_endpoints(self):
        """Test des endpoints de monitoring"""
        try:
            logger.info("📈 Test des endpoints de monitoring...")
            
            # Test des métriques
            response = self.session.get(f"{API_BASE_URL}/mlops/monitoring/metrics?hours=24")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ Endpoint des métriques accessible")
                logger.info(f"   Période: {data.get('period_hours', 0)} heures")
                logger.info(f"   Total prédictions: {data.get('total_predictions', 0)}")
                
                # Métriques de temps d'inférence
                inference_time = data.get('inference_time', {})
                if inference_time:
                    logger.info(f"   Temps d'inférence moyen: {inference_time.get('avg_ms', 0):.1f}ms")
            else:
                logger.warning(f"⚠️ Endpoint des métriques non accessible: {response.status_code}")
            
            # Test des alertes
            response = self.session.get(f"{API_BASE_URL}/mlops/monitoring/alerts")
            
            if response.status_code == 200:
                data = response.json()
                alerts = data.get('alerts', [])
                logger.info(f"✅ Endpoint des alertes accessible ({len(alerts)} alertes actives)")
                
                for alert in alerts[:3]:  # Affiche les 3 premières alertes
                    logger.info(f"   🚨 {alert.get('type')}: {alert.get('message')}")
                    logger.info(f"      Sévérité: {alert.get('severity')}")
                
                return True
            else:
                logger.error(f"❌ Échec du test des alertes: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du test de monitoring: {e}")
            return False
    
    def test_segmentation_with_monitoring(self):
        """Test de segmentation avec monitoring MLOps"""
        try:
            logger.info("🧠 Test de segmentation avec monitoring MLOps...")
            
            # Création d'une image de test
            from PIL import Image
            import numpy as np
            
            # Image de test simple
            test_image = Image.fromarray(
                np.random.randint(0, 255, (128, 128, 3), dtype=np.uint8)
            )
            test_image_path = "/tmp/test_brain_image.jpg"
            test_image.save(test_image_path)
            
            # Upload et segmentation
            with open(test_image_path, 'rb') as f:
                files = {'file': ('test_brain_image.jpg', f, 'image/jpeg')}
                response = self.session.post(
                    f"{API_BASE_URL}/segment",
                    files=files
                )
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ Segmentation avec monitoring réussie")
                logger.info(f"   Confiance: {data.get('confidence', 0):.1f}%")
                logger.info(f"   Temps de traitement: {data.get('processingTime', 'N/A')}")
                logger.info(f"   Tranche optimale: {data.get('optimalSlice', 'N/A')}")
                
                # Nettoyage
                os.remove(test_image_path)
                
                return True
            else:
                logger.error(f"❌ Échec de la segmentation: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du test de segmentation: {e}")
            return False
    
    def run_all_tests(self):
        """Exécute tous les tests MLOps"""
        logger.info("🚀 Démarrage des tests MLOps pour CereBloom Classify")
        logger.info("="*60)
        
        tests = [
            ("Authentification", self.authenticate),
            ("Santé MLOps", self.test_health_endpoint),
            ("Dashboard MLOps", self.test_dashboard_endpoint),
            ("Modèles MLOps", self.test_models_endpoint),
            ("Monitoring MLOps", self.test_monitoring_endpoints),
            ("Segmentation + Monitoring", self.test_segmentation_with_monitoring)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Test: {test_name}")
            logger.info("-" * 40)
            
            start_time = time.time()
            success = test_func()
            duration = time.time() - start_time
            
            results.append({
                "name": test_name,
                "success": success,
                "duration": duration
            })
            
            status = "✅ RÉUSSI" if success else "❌ ÉCHEC"
            logger.info(f"   Résultat: {status} ({duration:.2f}s)")
        
        # Résumé final
        logger.info("\n" + "="*60)
        logger.info("📋 RÉSUMÉ DES TESTS MLOPS")
        logger.info("="*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r["success"])
        total_time = sum(r["duration"] for r in results)
        
        logger.info(f"Total des tests: {total_tests}")
        logger.info(f"Tests réussis: {passed_tests}")
        logger.info(f"Tests échoués: {total_tests - passed_tests}")
        logger.info(f"Temps total: {total_time:.2f}s")
        logger.info(f"Taux de réussite: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\nDétail des résultats:")
        for result in results:
            status = "✅" if result["success"] else "❌"
            logger.info(f"  {status} {result['name']} ({result['duration']:.2f}s)")
        
        if passed_tests == total_tests:
            logger.info("\n🎉 TOUS LES TESTS MLOPS ONT RÉUSSI!")
            logger.info("🚀 Votre système MLOps est opérationnel!")
        else:
            logger.warning(f"\n⚠️ {total_tests - passed_tests} test(s) ont échoué")
            logger.warning("Vérifiez que le serveur est démarré et que MLOps est initialisé")
        
        logger.info("="*60)
        
        return passed_tests == total_tests

def check_server_availability():
    """Vérifie que le serveur est disponible"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Fonction principale"""
    
    print("🧪 TESTS MLOPS - CEREBLOOM CLASSIFY")
    print("="*60)
    
    # Vérification de la disponibilité du serveur
    logger.info("🔍 Vérification de la disponibilité du serveur...")
    
    if not check_server_availability():
        logger.error("❌ Serveur non disponible!")
        logger.error("Assurez-vous que le serveur FastAPI est démarré:")
        logger.error("  python start_server.py")
        return False
    
    logger.info("✅ Serveur disponible")
    
    # Exécution des tests
    tester = MLOpsAPITester()
    success = tester.run_all_tests()
    
    return success

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Tests MLOps pour CereBloom Classify")
    parser.add_argument("--quick", action="store_true", help="Tests rapides uniquement")
    
    args = parser.parse_args()
    
    success = main()
    sys.exit(0 if success else 1)

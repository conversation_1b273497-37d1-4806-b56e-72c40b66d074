"""
MLOps - Model Registry
Gestion centralisée des modèles de segmentation
"""

import os
import json
import hashlib
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ModelRegistry:
    """
    Registre centralisé pour la gestion des modèles ML
    """
    
    def __init__(self, registry_path: str = "mlops/models"):
        self.registry_path = Path(registry_path)
        self.registry_path.mkdir(parents=True, exist_ok=True)
        self.metadata_file = self.registry_path / "registry.json"
        self.models_metadata = self._load_registry()
        
    def _load_registry(self) -> Dict:
        """Charge le registre des modèles"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Erreur lors du chargement du registre: {e}")
        
        return {"models": {}, "versions": {}, "deployments": {}}
    
    def _save_registry(self):
        """Sauvegarde le registre des modèles"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.models_metadata, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde du registre: {e}")
    
    def _calculate_model_hash(self, model_path: str) -> str:
        """Calcule le hash d'un modèle pour la vérification d'intégrité"""
        hash_md5 = hashlib.md5()
        try:
            with open(model_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Erreur lors du calcul du hash: {e}")
            return ""
    
    def register_model(
        self,
        model_name: str,
        model_path: str,
        version: str,
        metadata: Dict[str, Any],
        tags: List[str] = None
    ) -> str:
        """
        Enregistre un nouveau modèle dans le registre
        
        Args:
            model_name: Nom du modèle
            model_path: Chemin vers le fichier du modèle
            version: Version du modèle
            metadata: Métadonnées du modèle
            tags: Tags pour la classification
            
        Returns:
            str: ID unique du modèle enregistré
        """
        try:
            # Génération de l'ID unique
            model_id = f"{model_name}_{version}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Calcul du hash pour l'intégrité
            model_hash = self._calculate_model_hash(model_path)
            
            # Copie du modèle dans le registre
            model_registry_path = self.registry_path / f"{model_id}.h5"
            shutil.copy2(model_path, model_registry_path)
            
            # Métadonnées complètes
            full_metadata = {
                "model_id": model_id,
                "model_name": model_name,
                "version": version,
                "file_path": str(model_registry_path),
                "file_hash": model_hash,
                "file_size": os.path.getsize(model_path),
                "registered_at": datetime.now().isoformat(),
                "tags": tags or [],
                "status": "registered",
                "metrics": metadata.get("metrics", {}),
                "training_info": metadata.get("training_info", {}),
                "model_config": metadata.get("model_config", {}),
                "dataset_info": metadata.get("dataset_info", {}),
                "performance": metadata.get("performance", {}),
                "deployment_history": []
            }
            
            # Enregistrement dans le registre
            self.models_metadata["models"][model_id] = full_metadata
            
            # Mise à jour des versions
            if model_name not in self.models_metadata["versions"]:
                self.models_metadata["versions"][model_name] = []
            self.models_metadata["versions"][model_name].append({
                "version": version,
                "model_id": model_id,
                "registered_at": datetime.now().isoformat()
            })
            
            self._save_registry()
            logger.info(f"Modèle enregistré: {model_id}")
            
            return model_id
            
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du modèle: {e}")
            raise
    
    def get_model(self, model_id: str) -> Optional[Dict]:
        """Récupère les métadonnées d'un modèle"""
        return self.models_metadata["models"].get(model_id)
    
    def get_latest_model(self, model_name: str) -> Optional[Dict]:
        """Récupère la dernière version d'un modèle"""
        versions = self.models_metadata["versions"].get(model_name, [])
        if not versions:
            return None
        
        latest_version = max(versions, key=lambda x: x["registered_at"])
        return self.get_model(latest_version["model_id"])
    
    def list_models(self, model_name: str = None, tags: List[str] = None) -> List[Dict]:
        """Liste les modèles avec filtres optionnels"""
        models = list(self.models_metadata["models"].values())
        
        if model_name:
            models = [m for m in models if m["model_name"] == model_name]
        
        if tags:
            models = [m for m in models if any(tag in m["tags"] for tag in tags)]
        
        return sorted(models, key=lambda x: x["registered_at"], reverse=True)
    
    def promote_model(self, model_id: str, stage: str) -> bool:
        """
        Promeut un modèle à un stage spécifique (staging, production)
        """
        try:
            model = self.get_model(model_id)
            if not model:
                return False
            
            # Mise à jour du stage
            model["stage"] = stage
            model["promoted_at"] = datetime.now().isoformat()
            
            # Historique des promotions
            if "promotion_history" not in model:
                model["promotion_history"] = []
            
            model["promotion_history"].append({
                "stage": stage,
                "promoted_at": datetime.now().isoformat()
            })
            
            self._save_registry()
            logger.info(f"Modèle {model_id} promu au stage: {stage}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la promotion du modèle: {e}")
            return False
    
    def archive_model(self, model_id: str) -> bool:
        """Archive un modèle (le marque comme obsolète)"""
        try:
            model = self.get_model(model_id)
            if not model:
                return False
            
            model["status"] = "archived"
            model["archived_at"] = datetime.now().isoformat()
            
            self._save_registry()
            logger.info(f"Modèle archivé: {model_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de l'archivage du modèle: {e}")
            return False
    
    def get_production_model(self, model_name: str) -> Optional[Dict]:
        """Récupère le modèle en production pour un nom donné"""
        models = self.list_models(model_name=model_name)
        production_models = [m for m in models if m.get("stage") == "production"]
        
        if production_models:
            return production_models[0]  # Le plus récent en production
        
        return None
    
    def validate_model_integrity(self, model_id: str) -> bool:
        """Valide l'intégrité d'un modèle via son hash"""
        try:
            model = self.get_model(model_id)
            if not model:
                return False
            
            current_hash = self._calculate_model_hash(model["file_path"])
            return current_hash == model["file_hash"]
            
        except Exception as e:
            logger.error(f"Erreur lors de la validation de l'intégrité: {e}")
            return False
    
    def get_registry_stats(self) -> Dict:
        """Statistiques du registre des modèles"""
        models = list(self.models_metadata["models"].values())
        
        stats = {
            "total_models": len(models),
            "models_by_status": {},
            "models_by_stage": {},
            "models_by_name": {},
            "total_size_mb": 0
        }
        
        for model in models:
            # Par statut
            status = model.get("status", "unknown")
            stats["models_by_status"][status] = stats["models_by_status"].get(status, 0) + 1
            
            # Par stage
            stage = model.get("stage", "none")
            stats["models_by_stage"][stage] = stats["models_by_stage"].get(stage, 0) + 1
            
            # Par nom
            name = model["model_name"]
            stats["models_by_name"][name] = stats["models_by_name"].get(name, 0) + 1
            
            # Taille totale
            stats["total_size_mb"] += model.get("file_size", 0) / (1024 * 1024)
        
        stats["total_size_mb"] = round(stats["total_size_mb"], 2)
        
        return stats

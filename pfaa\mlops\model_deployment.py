"""
MLOps - Model Deployment
Gestion du déploiement et des versions des modèles
"""

import os
import json
import shutil
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

from .model_registry import ModelRegistry
from .model_monitoring import ModelMonitor

logger = logging.getLogger(__name__)

class ModelDeployment:
    """
    Gestionnaire de déploiement des modèles ML
    """
    
    def __init__(
        self,
        deployment_path: str = "mlops/deployments",
        registry: ModelRegistry = None,
        monitor: ModelMonitor = None
    ):
        self.deployment_path = Path(deployment_path)
        self.deployment_path.mkdir(parents=True, exist_ok=True)
        
        self.registry = registry or ModelRegistry()
        self.monitor = monitor or ModelMonitor()
        
        # Configuration des environnements
        self.environments = {
            "development": {
                "path": self.deployment_path / "dev",
                "auto_deploy": True,
                "health_check_interval": 300,  # 5 minutes
                "rollback_enabled": True
            },
            "staging": {
                "path": self.deployment_path / "staging",
                "auto_deploy": False,
                "health_check_interval": 180,  # 3 minutes
                "rollback_enabled": True
            },
            "production": {
                "path": self.deployment_path / "prod",
                "auto_deploy": False,
                "health_check_interval": 60,   # 1 minute
                "rollback_enabled": True
            }
        }
        
        # Création des répertoires d'environnement
        for env_config in self.environments.values():
            env_config["path"].mkdir(parents=True, exist_ok=True)
        
        self.deployment_history = self._load_deployment_history()
    
    def _load_deployment_history(self) -> List[Dict]:
        """Charge l'historique des déploiements"""
        history_file = self.deployment_path / "deployment_history.json"
        if history_file.exists():
            try:
                with open(history_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Erreur lors du chargement de l'historique: {e}")
        return []
    
    def _save_deployment_history(self):
        """Sauvegarde l'historique des déploiements"""
        try:
            history_file = self.deployment_path / "deployment_history.json"
            with open(history_file, 'w') as f:
                json.dump(self.deployment_history, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde de l'historique: {e}")
    
    def deploy_model(
        self,
        model_id: str,
        environment: str,
        deployment_config: Dict = None,
        force: bool = False
    ) -> Dict:
        """
        Déploie un modèle dans un environnement spécifique
        
        Args:
            model_id: ID du modèle à déployer
            environment: Environnement cible (dev, staging, production)
            deployment_config: Configuration spécifique du déploiement
            force: Force le déploiement même si des vérifications échouent
            
        Returns:
            Dict: Résultat du déploiement
        """
        try:
            # Validation de l'environnement
            if environment not in self.environments:
                raise ValueError(f"Environnement invalide: {environment}")
            
            # Récupération du modèle
            model = self.registry.get_model(model_id)
            if not model:
                raise ValueError(f"Modèle non trouvé: {model_id}")
            
            # Vérifications pré-déploiement
            if not force:
                validation_result = self._validate_deployment(model, environment)
                if not validation_result["valid"]:
                    return {
                        "success": False,
                        "error": "Validation échouée",
                        "details": validation_result
                    }
            
            # Configuration du déploiement
            config = deployment_config or {}
            env_config = self.environments[environment]
            
            # Sauvegarde du modèle actuel (pour rollback)
            current_deployment = self._get_current_deployment(environment)
            
            # Déploiement du nouveau modèle
            deployment_id = f"{model_id}_{environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            deployment_path = env_config["path"] / deployment_id
            deployment_path.mkdir(parents=True, exist_ok=True)
            
            # Copie du modèle
            model_dest = deployment_path / "model.h5"
            shutil.copy2(model["file_path"], model_dest)
            
            # Création du fichier de configuration
            deployment_metadata = {
                "deployment_id": deployment_id,
                "model_id": model_id,
                "environment": environment,
                "deployed_at": datetime.now().isoformat(),
                "model_metadata": model,
                "deployment_config": config,
                "status": "deployed",
                "health_status": "unknown"
            }
            
            config_file = deployment_path / "deployment.json"
            with open(config_file, 'w') as f:
                json.dump(deployment_metadata, f, indent=2, default=str)
            
            # Mise à jour du lien symbolique vers le déploiement actuel
            current_link = env_config["path"] / "current"
            if current_link.exists() or current_link.is_symlink():
                current_link.unlink()
            current_link.symlink_to(deployment_id)
            
            # Enregistrement dans l'historique
            deployment_record = {
                "deployment_id": deployment_id,
                "model_id": model_id,
                "environment": environment,
                "deployed_at": datetime.now().isoformat(),
                "deployed_by": "system",  # À remplacer par l'utilisateur réel
                "status": "success",
                "previous_deployment": current_deployment.get("deployment_id") if current_deployment else None,
                "rollback_available": current_deployment is not None
            }
            
            self.deployment_history.append(deployment_record)
            self._save_deployment_history()
            
            # Mise à jour du registre des modèles
            if "deployment_history" not in model:
                model["deployment_history"] = []
            
            model["deployment_history"].append({
                "environment": environment,
                "deployment_id": deployment_id,
                "deployed_at": datetime.now().isoformat()
            })
            
            # Health check initial
            health_result = self._perform_health_check(environment)
            
            logger.info(f"Modèle {model_id} déployé avec succès en {environment}")
            
            return {
                "success": True,
                "deployment_id": deployment_id,
                "environment": environment,
                "model_id": model_id,
                "deployed_at": datetime.now().isoformat(),
                "health_check": health_result
            }
            
        except Exception as e:
            logger.error(f"Erreur lors du déploiement: {e}")
            return {
                "success": False,
                "error": str(e),
                "model_id": model_id,
                "environment": environment
            }
    
    def _validate_deployment(self, model: Dict, environment: str) -> Dict:
        """Valide un déploiement avant exécution"""
        validation_results = {
            "valid": True,
            "checks": {}
        }
        
        # Vérification de l'intégrité du modèle
        integrity_check = self.registry.validate_model_integrity(model["model_id"])
        validation_results["checks"]["model_integrity"] = integrity_check
        
        if not integrity_check:
            validation_results["valid"] = False
        
        # Vérification de l'espace disque
        env_path = self.environments[environment]["path"]
        disk_usage = shutil.disk_usage(env_path)
        free_space_gb = disk_usage.free / (1024**3)
        model_size_gb = model.get("file_size", 0) / (1024**3)
        
        space_check = free_space_gb > (model_size_gb * 2)  # 2x la taille du modèle
        validation_results["checks"]["disk_space"] = {
            "sufficient": space_check,
            "free_gb": round(free_space_gb, 2),
            "required_gb": round(model_size_gb * 2, 2)
        }
        
        if not space_check:
            validation_results["valid"] = False
        
        # Vérification des métriques du modèle (si disponibles)
        metrics = model.get("metrics", {})
        if metrics:
            accuracy = metrics.get("accuracy", 0)
            min_accuracy = 0.8  # 80% minimum
            
            accuracy_check = accuracy >= min_accuracy
            validation_results["checks"]["model_accuracy"] = {
                "sufficient": accuracy_check,
                "current": accuracy,
                "minimum": min_accuracy
            }
            
            if not accuracy_check:
                validation_results["valid"] = False
        
        return validation_results
    
    def _get_current_deployment(self, environment: str) -> Optional[Dict]:
        """Récupère le déploiement actuel d'un environnement"""
        try:
            env_config = self.environments[environment]
            current_link = env_config["path"] / "current"
            
            if current_link.exists() and current_link.is_symlink():
                deployment_path = current_link.resolve()
                config_file = deployment_path / "deployment.json"
                
                if config_file.exists():
                    with open(config_file, 'r') as f:
                        return json.load(f)
            
            return None
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du déploiement actuel: {e}")
            return None
    
    def _perform_health_check(self, environment: str) -> Dict:
        """Effectue un health check sur un déploiement"""
        try:
            current_deployment = self._get_current_deployment(environment)
            if not current_deployment:
                return {"status": "error", "message": "Aucun déploiement actuel"}
            
            model_path = self.environments[environment]["path"] / "current" / "model.h5"
            
            health_status = {
                "timestamp": datetime.now().isoformat(),
                "environment": environment,
                "deployment_id": current_deployment["deployment_id"],
                "checks": {}
            }
            
            # Vérification de l'existence du modèle
            health_status["checks"]["model_file_exists"] = model_path.exists()
            
            # Vérification de la taille du fichier
            if model_path.exists():
                file_size = model_path.stat().st_size
                expected_size = current_deployment["model_metadata"].get("file_size", 0)
                size_match = abs(file_size - expected_size) < 1024  # Tolérance de 1KB
                
                health_status["checks"]["model_file_size"] = {
                    "valid": size_match,
                    "current": file_size,
                    "expected": expected_size
                }
            
            # Test de chargement du modèle (simulation)
            try:
                # Ici, on pourrait charger le modèle pour vérifier qu'il fonctionne
                # Pour l'instant, on simule un test réussi
                health_status["checks"]["model_loadable"] = True
            except Exception as e:
                health_status["checks"]["model_loadable"] = False
                health_status["checks"]["load_error"] = str(e)
            
            # Statut global
            all_checks_passed = all(
                check if isinstance(check, bool) else check.get("valid", False)
                for check in health_status["checks"].values()
                if isinstance(check, (bool, dict))
            )
            
            health_status["status"] = "healthy" if all_checks_passed else "unhealthy"
            
            return health_status
            
        except Exception as e:
            logger.error(f"Erreur lors du health check: {e}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def rollback_deployment(self, environment: str) -> Dict:
        """Effectue un rollback vers le déploiement précédent"""
        try:
            # Recherche du déploiement précédent
            env_deployments = [
                d for d in self.deployment_history
                if d["environment"] == environment and d["status"] == "success"
            ]
            
            if len(env_deployments) < 2:
                return {
                    "success": False,
                    "error": "Aucun déploiement précédent disponible pour le rollback"
                }
            
            # Le déploiement précédent (avant le dernier)
            previous_deployment = env_deployments[-2]
            
            # Récupération du modèle précédent
            previous_model = self.registry.get_model(previous_deployment["model_id"])
            if not previous_model:
                return {
                    "success": False,
                    "error": f"Modèle précédent non trouvé: {previous_deployment['model_id']}"
                }
            
            # Déploiement du modèle précédent
            rollback_result = self.deploy_model(
                previous_deployment["model_id"],
                environment,
                {"rollback": True, "original_deployment": previous_deployment["deployment_id"]},
                force=True
            )
            
            if rollback_result["success"]:
                # Enregistrement du rollback dans l'historique
                rollback_record = {
                    "deployment_id": rollback_result["deployment_id"],
                    "model_id": previous_deployment["model_id"],
                    "environment": environment,
                    "deployed_at": datetime.now().isoformat(),
                    "deployed_by": "system",
                    "status": "rollback",
                    "rollback_from": env_deployments[-1]["deployment_id"],
                    "rollback_to": previous_deployment["deployment_id"]
                }
                
                self.deployment_history.append(rollback_record)
                self._save_deployment_history()
                
                logger.info(f"Rollback réussi en {environment} vers {previous_deployment['model_id']}")
            
            return rollback_result
            
        except Exception as e:
            logger.error(f"Erreur lors du rollback: {e}")
            return {
                "success": False,
                "error": str(e),
                "environment": environment
            }
    
    def get_deployment_status(self, environment: str = None) -> Dict:
        """Récupère le statut des déploiements"""
        try:
            if environment:
                environments = [environment]
            else:
                environments = list(self.environments.keys())
            
            status = {}
            
            for env in environments:
                current_deployment = self._get_current_deployment(env)
                health_status = self._perform_health_check(env)
                
                status[env] = {
                    "current_deployment": current_deployment,
                    "health_status": health_status,
                    "last_deployment": self._get_last_deployment(env)
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du statut: {e}")
            return {"error": str(e)}
    
    def _get_last_deployment(self, environment: str) -> Optional[Dict]:
        """Récupère le dernier déploiement d'un environnement"""
        env_deployments = [
            d for d in self.deployment_history
            if d["environment"] == environment
        ]
        
        return env_deployments[-1] if env_deployments else None
    
    def list_deployments(self, environment: str = None, limit: int = 10) -> List[Dict]:
        """Liste les déploiements avec filtres optionnels"""
        deployments = self.deployment_history
        
        if environment:
            deployments = [d for d in deployments if d["environment"] == environment]
        
        # Tri par date (plus récent en premier)
        deployments = sorted(deployments, key=lambda x: x["deployed_at"], reverse=True)
        
        return deployments[:limit]

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  PlusCircle, 
  Search, 
  MoreHorizontal, 
  Pencil, 
  Trash2, 
  Eye,
  Shield,
  User as UserIcon
} from 'lucide-react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth, UserRole } from '@/contexts/AuthContext';

// Mock user data
const mockUsers = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin' as UserRole,
    status: 'active',
    lastLogin: '2023-11-20T10:30:00Z',
    createdAt: '2023-01-15T08:00:00Z',
  },
  {
    id: '2',
    name: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
    role: 'doctor' as UserRole,
    status: 'active',
    lastLogin: '2023-11-19T14:45:00Z',
    createdAt: '2023-02-10T09:30:00Z',
  },
  {
    id: '3',
    name: 'Dr. Michael Chen',
    email: '<EMAIL>',
    role: 'doctor' as UserRole,
    status: 'active',
    lastLogin: '2023-11-18T11:20:00Z',
    createdAt: '2023-03-05T10:15:00Z',
  },
  {
    id: '4',
    name: 'Dr. Emily Rodriguez',
    email: '<EMAIL>',
    role: 'doctor' as UserRole,
    status: 'inactive',
    lastLogin: '2023-10-30T09:10:00Z',
    createdAt: '2023-04-20T08:45:00Z',
  },
  {
    id: '5',
    name: 'System Administrator',
    email: '<EMAIL>',
    role: 'admin' as UserRole,
    status: 'active',
    lastLogin: '2023-11-20T08:15:00Z',
    createdAt: '2023-01-01T00:00:00Z',
  },
];

interface NewUserFormData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
}

const Users: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { register } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState(mockUsers);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState<NewUserFormData>({
    name: '',
    email: '',
    password: '',
    role: 'doctor',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeleteUser = (id: string) => {
    setUsers(users.filter(user => user.id !== id));
    toast({
      title: t('users.userDeleted'),
      description: t('users.userDeleted'),
    });
    setUserToDelete(null);
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // In a real app, this would call the register function from AuthContext
      // await register(newUser.email, newUser.password, newUser.name, newUser.role);
      
      // For now, we'll just add the user to our local state
      const newId = (users.length + 1).toString();
      setUsers([
        ...users,
        {
          id: newId,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          status: 'active',
          lastLogin: '-',
          createdAt: new Date().toISOString(),
        },
      ]);

      toast({
        title: t('users.userCreated'),
        description: t('users.userCreated'),
      });

      // Reset form and close dialog
      setNewUser({
        name: '',
        email: '',
        password: '',
        role: 'doctor',
      });
      setIsAddUserDialogOpen(false);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: String(error),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('users.userList')}</h1>
            <p className="text-muted-foreground">
              {t('users.userList')}
            </p>
          </div>
          <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
            <DialogTrigger asChild>
              <Button className="mt-4 md:mt-0">
                <PlusCircle className="mr-2 h-4 w-4" />
                {t('users.addUser')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('users.addUser')}</DialogTitle>
                <DialogDescription>
                  Fill in the form below to create a new user account.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddUser}>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name">{t('common.name')}</Label>
                    <Input
                      id="name"
                      value={newUser.name}
                      onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email">{t('common.email')}</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="password">{t('common.password')}</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label>{t('common.role')}</Label>
                    <div className="flex space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="doctor"
                          name="role"
                          value="doctor"
                          checked={newUser.role === 'doctor'}
                          onChange={() => setNewUser({ ...newUser, role: 'doctor' })}
                          className="h-4 w-4 text-medical border-slate-300 focus:ring-medical"
                        />
                        <Label htmlFor="doctor" className="cursor-pointer">
                          {t('common.doctor')}
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="admin"
                          name="role"
                          value="admin"
                          checked={newUser.role === 'admin'}
                          onChange={() => setNewUser({ ...newUser, role: 'admin' })}
                          className="h-4 w-4 text-medical border-slate-300 focus:ring-medical"
                        />
                        <Label htmlFor="admin" className="cursor-pointer">
                          {t('common.admin')}
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsAddUserDialogOpen(false)}>
                    {t('common.cancel')}
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? t('common.loading') : t('common.save')}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('common.search')}
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('users.userId')}</TableHead>
                <TableHead>{t('users.userName')}</TableHead>
                <TableHead>{t('users.userEmail')}</TableHead>
                <TableHead>{t('users.userRole')}</TableHead>
                <TableHead className="hidden md:table-cell">{t('users.userStatus')}</TableHead>
                <TableHead className="hidden lg:table-cell">{t('users.lastLogin')}</TableHead>
                <TableHead className="text-right">{t('common.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                    {t('users.noUsers')}
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.id}</TableCell>
                    <TableCell>{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge variant={user.role === 'admin' ? 'default' : 'outline'}>
                        {user.role === 'admin' ? (
                          <Shield className="mr-1 h-3 w-3" />
                        ) : (
                          <UserIcon className="mr-1 h-3 w-3" />
                        )}
                        {user.role === 'admin' ? t('common.admin') : t('common.doctor')}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <Badge
                        variant={user.status === 'active' ? 'outline' : 'secondary'}
                        className={
                          user.status === 'active'
                            ? 'bg-green-50 text-green-700 hover:bg-green-50 hover:text-green-700'
                            : 'bg-slate-100 text-slate-500'
                        }
                      >
                        {user.status === 'active' ? t('users.active') : t('users.inactive')}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {new Date(user.lastLogin).toLocaleString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>{t('common.actions')}</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            <span>{t('common.view')}</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Pencil className="mr-2 h-4 w-4" />
                            <span>{t('common.edit')}</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <AlertDialog open={userToDelete === user.id} onOpenChange={(open) => !open && setUserToDelete(null)}>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem
                                className="text-red-500 focus:text-red-500"
                                onSelect={(e) => {
                                  e.preventDefault();
                                  setUserToDelete(user.id);
                                }}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>{t('common.delete')}</span>
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>{t('users.deleteUser')}</AlertDialogTitle>
                                <AlertDialogDescription>
                                  {t('users.deleteConfirmation')}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
                                <AlertDialogAction
                                  className="bg-red-500 hover:bg-red-600"
                                  onClick={() => handleDeleteUser(user.id)}
                                >
                                  {t('common.delete')}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Users;

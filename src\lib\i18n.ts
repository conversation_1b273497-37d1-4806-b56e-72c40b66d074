import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Define resources object with translations
const resources = {
  en: {
    translation: {
      common: {
        appName: 'NeuroScan',
        loading: 'Loading...',
        error: 'An error occurred',
        save: 'Save',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        view: 'View',
        search: 'Search',
        filter: 'Filter',
        filters: 'Filters',
        advancedFilters: 'Advanced Filters',
        resetFilters: 'Reset Filters',
        noResults: 'No results found',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        submit: 'Submit',
        reset: 'Reset',
        logout: 'Logout',
        login: 'Login',
        register: 'Register',
        email: 'Email',
        password: 'Password',
        confirmPassword: 'Confirm Password',
        name: 'Name',
        role: 'Role',
        admin: 'Admin',
        doctor: 'Doctor',
        darkMode: 'Dark Mode',
        lightMode: 'Light Mode',
        language: 'Language',
        english: 'English',
        french: 'French',
        portuguese: 'Portuguese',
        footerDescription: 'Advanced cerebral tumor classification using cutting-edge AI technology to assist medical professionals in accurate diagnosis and treatment planning.',
        copyright: '© {year} NeuroScan. All rights reserved. For educational purposes only.',
        actions: 'Actions',
        export: 'Export',
        exportPDF: 'Export as PDF',
        exportExcel: 'Export as Excel',
        tableView: 'Table View',
        cardView: 'Card View',
        all: 'All',
        male: 'Male',
        female: 'Female',
        other: 'Other',
        selectGender: 'Select Gender',
        selectDoctor: 'Select Doctor',
        selectTumorType: 'Select Tumor Type',
        min: 'Min',
        max: 'Max',
        startDate: 'Start Date',
        endDate: 'End Date',
        showing: 'Showing',
        of: 'of',
        page: 'Page',
        yearsOld: 'years old',
        comingSoon: 'Coming Soon',
      },
      navigation: {
        home: 'Home',
        about: 'About',
        dashboard: 'Dashboard',
        patients: 'Patients',
        scans: 'Scans',
        users: 'Users',
        settings: 'Settings',
        profile: 'Profile',
      },
      auth: {
        loginTitle: 'Login to your account',
        loginSubtitle: 'Enter your credentials to access your account',
        registerTitle: 'Create a new account',
        registerSubtitle: 'Fill in the form to create your account',
        forgotPassword: 'Forgot password?',
        noAccount: 'Don\'t have an account?',
        hasAccount: 'Already have an account?',
        createAccount: 'Create account',
        loginInstead: 'Login instead',
        loginSuccess: 'Login successful',
        loginError: 'Invalid email or password',
        registerSuccess: 'Registration successful',
        registerError: 'Registration failed',
        logoutSuccess: 'Logout successful',
        unauthorized: 'Unauthorized access',
        unauthorizedMessage: 'You do not have permission to access this page',
      },
      dashboard: {
        welcome: 'Welcome',
        recentScans: 'Recent Scans',
        patientCount: 'Total Patients',
        scanCount: 'Total Scans',
        pendingScans: 'Pending Scans',
        completedScans: 'Completed Scans',
        quickActions: 'Quick Actions',
        viewAllPatients: 'View All Patients',
        addNewPatient: 'Add New Patient',
        uploadNewScan: 'Upload New Scan',
        manageUsers: 'Manage Users',
      },
      patients: {
        patientList: 'Patient List',
        addPatient: 'Add Patient',
        editPatient: 'Edit Patient',
        deletePatient: 'Delete Patient',
        patientDetails: 'Patient Details',
        patientId: 'Patient ID',
        firstName: 'First Name',
        lastName: 'Last Name',
        dateOfBirth: 'Date of Birth',
        gender: 'Gender',
        male: 'Male',
        female: 'Female',
        other: 'Other',
        contactNumber: 'Contact Number',
        address: 'Address',
        medicalHistory: 'Medical History',
        assignedDoctor: 'Assigned Doctor',
        noPatients: 'No patients found',
        deleteConfirmation: 'Are you sure you want to delete this patient?',
        patientCreated: 'Patient created successfully',
        patientUpdated: 'Patient updated successfully',
        patientDeleted: 'Patient deleted successfully',
        patients: 'Patients',
        ageRange: 'Age Range',
        admissionDate: 'Admission Date',
        lastVisit: 'Last Visit',
        nextAppointment: 'Next Appointment',
        bloodType: 'Blood Type',
        email: 'Email',
        height: 'Height',
        weight: 'Weight',
        emergencyContact: 'Emergency Contact',
        relationship: 'Relationship',
        insurance: 'Insurance',
        insuranceInformation: 'Insurance Information',
        provider: 'Provider',
        policyNumber: 'Policy Number',
        expiryDate: 'Expiry Date',
        allergies: 'Allergies',
        noAllergies: 'No known allergies',
        chronicConditions: 'Chronic Conditions',
        noChronicConditions: 'No chronic conditions',
        pastSurgeries: 'Past Surgeries',
        noPastSurgeries: 'No past surgeries',
        familyHistory: 'Family History',
        noFamilyHistory: 'No family history recorded',
        currentMedications: 'Current Medications',
        noCurrentMedications: 'No current medications',
        notes: 'Notes',
        noNotes: 'No notes available',
        timeline: 'Timeline',
        noEvents: 'No events recorded',
        medicalInformation: 'Medical Information',
        patientNotFound: 'Patient Not Found',
        patientNotFoundDescription: 'The patient you are looking for does not exist or has been removed.',
        noUpcomingAppointments: 'No upcoming appointments',
        editPatientDescription: 'Edit patient information and medical history.',
      },
      scans: {
        scanList: 'Scan List',
        addScan: 'Add Scan',
        uploadScan: 'Upload Scan',
        scanDetails: 'Scan Details',
        scanId: 'Scan ID',
        scanDate: 'Scan Date',
        patientScansDescription: 'View and compare patient scan history.',
        scanType: 'Scan Type',
        scanResult: 'Scan Result',
        scanStatus: 'Scan Status',
        pending: 'Pending',
        processing: 'Processing',
        completed: 'Completed',
        failed: 'Failed',
        notes: 'Notes',
        noScans: 'No scans found',
        uploadInstructions: 'Drag and drop or click to browse',
        supportedFormats: 'Supports JPEG, PNG, GIF, BMP, TIFF (Max 10MB)',
        analyzing: 'Analyzing brain scan... Please wait',
        invalidFileType: 'Please upload a valid image file (JPEG, PNG, GIF, BMP, TIFF)',
        fileSizeExceeded: 'File size exceeds 10MB limit',
        processingImage: 'Processing image...',
        imageReady: 'Image ready for processing',
        classificationResults: 'Classification Results',
        confidence: 'Confidence Score',
        highConfidence: 'High Confidence',
        mediumConfidence: 'Medium Confidence',
        lowConfidence: 'Low Confidence',
        recommendations: 'Recommendations',
        tumorCharacteristics: 'Tumor Characteristics',
        technicalDetails: 'Technical Details',
        generateReport: 'Generate Detailed Report',
        newScan: 'New Scan',
        uploadNewScan: 'Upload New Scan',
        examHistory: 'Exam History',
        gallery: 'Gallery',
        comparison: 'Comparison',
        evolution: 'Evolution',
        scanGallery: 'Scan Gallery',
        scansAvailable: 'scans available',
        scanComparison: 'Scan Comparison',
        scanComparisonDescription: 'Compare two scans side by side to track changes over time',
        selectScan: 'Select a scan',
        comparisonAnalysis: 'Comparison Analysis',
        timeBetweenScans: 'Time between scans',
        sizeChange: 'Size change',
        increased: 'Increased',
        decreased: 'Decreased',
        unchanged: 'Unchanged',
        zoomLevel: 'Zoom level',
        tumorEvolution: 'Tumor Evolution',
        tumorEvolutionDescription: 'Track changes in tumor size over time',
        tumorSizeChartInfo: 'This chart shows the evolution of tumor size based on measurements from MRI scans',
        tumorSizeCm: 'Tumor Size (cm)',
        initialSize: 'Initial Size',
        currentSize: 'Current Size',
        decreaseSinceFirst: 'Decrease since first scan',
        increaseSinceFirst: 'Increase since first scan',
        noChangeSinceFirst: 'No change since first scan',
        notEnoughDataForChart: 'Not enough data to generate chart. At least two scans with tumor size measurements are required.',
        notEnoughScans: 'At least two scans are required for comparison',
        totalScans: 'Total Scans',
        lastScan: 'Last Scan',
        size: 'Size',
        type: 'Type',
        aiClassificationResults: 'AI Classification Results',
        aiClassificationDescription: 'Latest analysis and diagnosis based on AI classification',
        latestDiagnosis: 'Latest Diagnosis',
        tumorType: 'Tumor Type',
        tumorSize: 'Tumor Size',
        tumorLocation: 'Tumor Location',
        malignant: 'Malignant',
        benign: 'Benign',
        recommendedAction: 'Recommended Action',
        continueTreatment: 'Continue Treatment',
        monitoringRecommended: 'Monitoring Recommended',
        basedOnLatestScan: 'Based on latest scan results',
      },
      users: {
        userList: 'User List',
        addUser: 'Add User',
        editUser: 'Edit User',
        deleteUser: 'Delete User',
        userDetails: 'User Details',
        userId: 'User ID',
        userName: 'User Name',
        userEmail: 'Email',
        userRole: 'Role',
        userStatus: 'Status',
        active: 'Active',
        inactive: 'Inactive',
        suspended: 'Suspended',
        lastLogin: 'Last Login',
        createdAt: 'Created At',
        noUsers: 'No users found',
        deleteConfirmation: 'Are you sure you want to delete this user?',
        userCreated: 'User created successfully',
        userUpdated: 'User updated successfully',
        userDeleted: 'User deleted successfully',
      },
      treatments: {
        treatmentTracking: 'Treatment Tracking',
        treatmentPlan: 'Treatment Plan',
        treatmentHistory: 'Treatment History',
        currentTreatmentPlan: 'Current Treatment Plan',
        currentTreatmentPlanDescription: 'Active treatments and medications for this patient',
        treatmentHistoryDescription: 'Past treatments and their outcomes',
        noActiveTreatments: 'No active treatments found',
        createTreatmentPlan: 'Create Treatment Plan',
        startDate: 'Start Date',
        endDate: 'End Date',
        frequency: 'Frequency',
        dosage: 'Dosage',
        prescribedBy: 'Prescribed By',
        effectiveness: 'Effectiveness',
        sideEffects: 'Side Effects',
        progress: 'Progress',
        notes: 'Notes',
        additionalTreatments: 'Additional Treatments',
        viewFullDetails: 'View Full Details',
        updateTreatmentPlan: 'Update Treatment Plan',
        completedTreatments: 'Completed Treatments',
        cancelledTreatments: 'Cancelled Treatments',
        noTreatmentHistory: 'No treatment history found',
        cancellationReason: 'Cancellation Reason',
        notSpecified: 'Not Specified',
        exportTreatmentHistory: 'Export Treatment History',
        addTreatment: 'Add Treatment',
        activeTreatments: 'Active Treatments',
        activeTreatmentSingular: 'active treatment',
        activeTreatmentPlural: 'active treatments',
        plan: 'Plan',
        history: 'History',
        treatmentEffectiveness: 'Treatment Effectiveness',
        treatmentEffectivenessDescription: 'Overall assessment of current treatment efficacy',
        overallResponse: 'Overall Response',
        positive: 'Positive',
        moderate: 'Moderate',
        insufficient: 'Insufficient',
        basedOnCurrentTreatments: 'Based on current treatments',
        significant: 'Significant',
        mild: 'Mild',
        minimal: 'Minimal',
        monitoringRecommended: 'Monitoring recommended',
        nextSteps: 'Next Steps',
        continueCurrentPlan: 'Continue Current Plan',
        scheduleFollowUp: 'Schedule Follow-up',
        recommendedByDoctor: 'Recommended by doctor',
      },

      appointments: {
        appointments: 'Appointments',
        appointmentCalendar: 'Appointment Calendar',
        appointmentCalendarDescription: 'Schedule and manage patient appointments',
        addAppointment: 'Add Appointment',
        noAppointmentsForDate: 'No appointments for this date',
        upcomingAppointments: 'Upcoming Appointments',
        noUpcomingAppointments: 'No upcoming appointments',
        upcomingAppointmentSingular: 'upcoming appointment',
        upcomingAppointmentPlural: 'upcoming appointments',
        date: 'Date',
        time: 'Time',
        doctor: 'Doctor',
        purpose: 'Purpose',
        status: 'Status',
        followUp: 'Follow-up',
        cancel: 'Cancel',
        confirm: 'Confirm',
        viewSummary: 'View Summary',
        viewDetails: 'View Details',
        viewAllAppointments: 'View All Appointments',
      },

      medications: {
        medications: 'Medications',
        medicationTracking: 'Medication Tracking',
        medicationTrackingDescription: 'Track and manage patient medications',
        noMedications: 'No medications found',
        addMedication: 'Add Medication',
        currentMedications: 'Current Medications',
        pastMedications: 'Past Medications',
        noCurrentMedications: 'No current medications',
        noPastMedications: 'No past medications',
        frequency: 'Frequency',
        dosage: 'Dosage',
        startDate: 'Start Date',
        endDate: 'End Date',
        reportedSideEffects: 'Reported Side Effects',
        adherence: 'Adherence',
        effectiveness: 'Effectiveness',
        markAsTaken: 'Mark as Taken',
        todaySchedule: 'Today\'s Schedule',
        noMedicationsScheduled: 'No medications scheduled for today',
        sideEffectsReporting: 'Side Effects Reporting',
        reportSideEffect: 'Report Side Effect',
        sideEffectsImportance: 'Important: Report Any Side Effects',
        sideEffectsDescription: 'If you experience any side effects from your medications, please report them immediately to your healthcare provider.',
        activeMedications: 'Active medications',
      },

      classification: {
        title: 'Tumor Classification Tool',
        subtitle: 'Upload an MRI scan image to get instant AI-powered classification results with detailed analysis and recommendations.',
        aiPowered: 'AI-Powered Analysis',
        neuralNetwork: 'Neural Network Powered',
        accuracy: '98.7% Accuracy',
        geneticCorrelation: 'Genetic Correlation',
        uploadScan: 'Upload MRI Scan',
        results: 'Results',
        overview: 'Overview',
        details: 'Details',
        size: 'Size',
        location: 'Location',
        density: 'Density',
        borders: 'Borders',
        model: 'Model',
        resolution: 'Resolution',
        scanType: 'Scan Type',
        processingTime: 'Processing Time',
        disclaimer: 'This tool is designed for educational purposes only and should not be used as a replacement for professional medical advice, diagnosis, or treatment.',
      },
      about: {
        title: 'About NeuroScan',
        subtitle: 'Advanced technology meeting medical expertise to revolutionize cerebral tumor diagnosis.',
        tryOurTool: 'Try Our Tool',
        mission: 'Our Mission',
        missionTitle: 'Advancing Neurological Diagnostics Through AI',
        missionText1: 'NeuroScan was founded with a clear mission: to combine cutting-edge artificial intelligence with neurological expertise to provide faster, more accurate diagnosis of cerebral tumors.',
        missionText2: 'By creating accessible tools for medical professionals, we aim to improve patient outcomes and reduce the time from imaging to diagnosis and treatment.',
        improvingPatientCare: 'Improving Patient Care',
        reducingDiagnosisTime: 'Reducing Diagnosis Time',
        technology: 'Our Technology',
        technologyTitle: 'State-of-the-Art Classification',
        technologyText: 'Our neural network model has been trained on thousands of MRI scans and validated by expert neuroradiologists to ensure reliable performance.',
        deepLearning: 'Deep Learning',
        deepLearningText: 'Utilizing convolutional neural networks optimized for medical imaging analysis with 98.7% validated accuracy.',
        statisticalAnalysis: 'Statistical Analysis',
        statisticalAnalysisText: 'Advanced statistical models to reduce false positives and provide confidence intervals for classification results.',
        geneticCorrelation: 'Genetic Correlation',
        geneticCorrelationText: 'Mapping radiographic features to known genetic markers to provide insights into potential treatment approaches.',
        expertise: 'Our Expertise',
        expertiseTitle: 'Interdisciplinary Collaboration',
        expertiseText: 'NeuroScan brings together experts from neurology, radiology, computer science, and data science to create a truly comprehensive approach.',
        getStarted: 'Get Started',
        getStartedTitle: 'Ready to Experience NeuroScan?',
        getStartedText: 'Try our classification tool now and see how AI can enhance your diagnostic workflow.',
        startClassification: 'Start Classification',
        requestApiAccess: 'Request API Access',
      },
      notFound: {
        title: '404',
        message: 'Oops! Page not found',
        returnHome: 'Return to Home',
      },
    }
  },
  fr: {
    translation: {
      common: {
        appName: 'NeuroScan',
        loading: 'Chargement...',
        error: 'Une erreur est survenue',
        save: 'Enregistrer',
        cancel: 'Annuler',
        delete: 'Supprimer',
        edit: 'Modifier',
        view: 'Voir',
        search: 'Rechercher',
        filter: 'Filtrer',
        filters: 'Filtres',
        advancedFilters: 'Filtres avancés',
        resetFilters: 'Réinitialiser les filtres',
        noResults: 'Aucun résultat trouvé',
        back: 'Retour',
        next: 'Suivant',
        previous: 'Précédent',
        submit: 'Soumettre',
        reset: 'Réinitialiser',
        logout: 'Déconnexion',
        login: 'Connexion',
        register: 'S\'inscrire',
        email: 'Email',
        password: 'Mot de passe',
        confirmPassword: 'Confirmer le mot de passe',
        name: 'Nom',
        role: 'Rôle',
        admin: 'Administrateur',
        doctor: 'Médecin',
        darkMode: 'Mode sombre',
        lightMode: 'Mode clair',
        language: 'Langue',
        english: 'Anglais',
        french: 'Français',
        portuguese: 'Portugais',
        footerDescription: 'Classification avancée des tumeurs cérébrales utilisant une technologie d\'IA de pointe pour aider les professionnels de la santé dans le diagnostic précis et la planification du traitement.',
        copyright: '© {year} NeuroScan. Tous droits réservés. À des fins éducatives uniquement.',
        actions: 'Actions',
        export: 'Exporter',
        exportPDF: 'Exporter en PDF',
        exportExcel: 'Exporter en Excel',
        tableView: 'Vue tableau',
        cardView: 'Vue carte',
        all: 'Tous',
        male: 'Masculin',
        female: 'Féminin',
        other: 'Autre',
        selectGender: 'Sélectionner le genre',
        selectDoctor: 'Sélectionner le médecin',
        selectTumorType: 'Sélectionner le type de tumeur',
        min: 'Min',
        max: 'Max',
        startDate: 'Date de début',
        endDate: 'Date de fin',
        showing: 'Affichage de',
        of: 'sur',
        page: 'Page',
        yearsOld: 'ans',
        comingSoon: 'Bientôt disponible',
      },
      navigation: {
        home: 'Accueil',
        about: 'À propos',
        dashboard: 'Tableau de bord',
        patients: 'Patients',
        scans: 'Scans',
        users: 'Utilisateurs',
        settings: 'Paramètres',
        profile: 'Profil',
      },
      auth: {
        loginTitle: 'Connectez-vous à votre compte',
        loginSubtitle: 'Entrez vos identifiants pour accéder à votre compte',
        registerTitle: 'Créer un nouveau compte',
        registerSubtitle: 'Remplissez le formulaire pour créer votre compte',
        forgotPassword: 'Mot de passe oublié?',
        noAccount: 'Vous n\'avez pas de compte?',
        hasAccount: 'Vous avez déjà un compte?',
        createAccount: 'Créer un compte',
        loginInstead: 'Se connecter',
        loginSuccess: 'Connexion réussie',
        loginError: 'Email ou mot de passe invalide',
        registerSuccess: 'Inscription réussie',
        registerError: 'Échec de l\'inscription',
        logoutSuccess: 'Déconnexion réussie',
        unauthorized: 'Accès non autorisé',
        unauthorizedMessage: 'Vous n\'avez pas la permission d\'accéder à cette page',
      },
      dashboard: {
        welcome: 'Bienvenue',
        recentScans: 'Scans récents',
        patientCount: 'Total des patients',
        scanCount: 'Total des scans',
        pendingScans: 'Scans en attente',
        completedScans: 'Scans terminés',
        quickActions: 'Actions rapides',
        viewAllPatients: 'Voir tous les patients',
        addNewPatient: 'Ajouter un nouveau patient',
        uploadNewScan: 'Télécharger un nouveau scan',
        manageUsers: 'Gérer les utilisateurs',
      },
      patients: {
        patientList: 'Liste des patients',
        addPatient: 'Ajouter un patient',
        editPatient: 'Modifier le patient',
        deletePatient: 'Supprimer le patient',
        patientDetails: 'Détails du patient',
        patientId: 'ID du patient',
        firstName: 'Prénom',
        lastName: 'Nom',
        dateOfBirth: 'Date de naissance',
        gender: 'Genre',
        male: 'Masculin',
        female: 'Féminin',
        other: 'Autre',
        contactNumber: 'Numéro de contact',
        address: 'Adresse',
        medicalHistory: 'Antécédents médicaux',
        assignedDoctor: 'Médecin assigné',
        noPatients: 'Aucun patient trouvé',
        deleteConfirmation: 'Êtes-vous sûr de vouloir supprimer ce patient?',
        patientCreated: 'Patient créé avec succès',
        patientUpdated: 'Patient mis à jour avec succès',
        patientDeleted: 'Patient supprimé avec succès',
        patients: 'Patients',
        ageRange: 'Tranche d\'âge',
        admissionDate: 'Date d\'admission',
        lastVisit: 'Dernière visite',
        nextAppointment: 'Prochain rendez-vous',
        bloodType: 'Groupe sanguin',
        email: 'Email',
        height: 'Taille',
        weight: 'Poids',
        emergencyContact: 'Contact d\'urgence',
        relationship: 'Relation',
        insurance: 'Assurance',
        insuranceInformation: 'Informations d\'assurance',
        provider: 'Fournisseur',
        policyNumber: 'Numéro de police',
        expiryDate: 'Date d\'expiration',
        allergies: 'Allergies',
        noAllergies: 'Aucune allergie connue',
        chronicConditions: 'Maladies chroniques',
        noChronicConditions: 'Aucune maladie chronique',
        pastSurgeries: 'Chirurgies antérieures',
        noPastSurgeries: 'Aucune chirurgie antérieure',
        familyHistory: 'Antécédents familiaux',
        noFamilyHistory: 'Aucun antécédent familial enregistré',
        currentMedications: 'Médicaments actuels',
        noCurrentMedications: 'Aucun médicament actuel',
        notes: 'Notes',
        noNotes: 'Aucune note disponible',
        timeline: 'Chronologie',
        noEvents: 'Aucun événement enregistré',
        medicalInformation: 'Informations médicales',
        patientNotFound: 'Patient non trouvé',
        patientNotFoundDescription: 'Le patient que vous recherchez n\'existe pas ou a été supprimé.',
        noUpcomingAppointments: 'Aucun rendez-vous à venir',
        editPatientDescription: 'Modifier les informations et l\'historique médical du patient.',
      },
      scans: {
        scanList: 'Liste des scans',
        addScan: 'Ajouter un scan',
        uploadScan: 'Télécharger un scan',
        scanDetails: 'Détails du scan',
        scanId: 'ID du scan',
        scanDate: 'Date du scan',
        patientScansDescription: 'Visualiser et comparer l\'historique des scans du patient.',
        scanType: 'Type de scan',
        scanResult: 'Résultat du scan',
        scanStatus: 'Statut du scan',
        pending: 'En attente',
        processing: 'En cours de traitement',
        completed: 'Terminé',
        failed: 'Échoué',
        notes: 'Notes',
        noScans: 'Aucun scan trouvé',
        uploadInstructions: 'Glisser-déposer ou cliquer pour parcourir',
        supportedFormats: 'Supporte JPEG, PNG, GIF, BMP, TIFF (Max 10MB)',
        analyzing: 'Analyse du scan cérébral... Veuillez patienter',
        invalidFileType: 'Veuillez télécharger un fichier image valide (JPEG, PNG, GIF, BMP, TIFF)',
        fileSizeExceeded: 'La taille du fichier dépasse la limite de 10 Mo',
        processingImage: 'Traitement de l\'image...',
        imageReady: 'Image prête pour le traitement',
        classificationResults: 'Résultats de classification',
        confidence: 'Score de confiance',
        highConfidence: 'Confiance élevée',
        mediumConfidence: 'Confiance moyenne',
        lowConfidence: 'Confiance faible',
        recommendations: 'Recommandations',
        tumorCharacteristics: 'Caractéristiques de la tumeur',
        technicalDetails: 'Détails techniques',
        generateReport: 'Générer un rapport détaillé',
        newScan: 'Nouveau scan',
      },
      users: {
        userList: 'Liste des utilisateurs',
        addUser: 'Ajouter un utilisateur',
        editUser: 'Modifier l\'utilisateur',
        deleteUser: 'Supprimer l\'utilisateur',
        userDetails: 'Détails de l\'utilisateur',
        userId: 'ID de l\'utilisateur',
        userName: 'Nom d\'utilisateur',
        userEmail: 'Email',
        userRole: 'Rôle',
        userStatus: 'Statut',
        active: 'Actif',
        inactive: 'Inactif',
        suspended: 'Suspendu',
        lastLogin: 'Dernière connexion',
        createdAt: 'Créé le',
        noUsers: 'Aucun utilisateur trouvé',
        deleteConfirmation: 'Êtes-vous sûr de vouloir supprimer cet utilisateur?',
        userCreated: 'Utilisateur créé avec succès',
        userUpdated: 'Utilisateur mis à jour avec succès',
        userDeleted: 'Utilisateur supprimé avec succès',
      },
      classification: {
        title: 'Outil de classification des tumeurs',
        subtitle: 'Téléchargez une image d\'IRM pour obtenir des résultats de classification instantanés alimentés par l\'IA avec une analyse détaillée et des recommandations.',
        aiPowered: 'Analyse alimentée par l\'IA',
        neuralNetwork: 'Alimenté par réseau neuronal',
        accuracy: 'Précision de 98,7%',
        geneticCorrelation: 'Corrélation génétique',
        uploadScan: 'Télécharger l\'IRM',
        results: 'Résultats',
        overview: 'Aperçu',
        details: 'Détails',
        size: 'Taille',
        location: 'Emplacement',
        density: 'Densité',
        borders: 'Bordures',
        model: 'Modèle',
        resolution: 'Résolution',
        scanType: 'Type de scan',
        processingTime: 'Temps de traitement',
        disclaimer: 'Cet outil est conçu à des fins éducatives uniquement et ne doit pas être utilisé comme un substitut à un avis médical professionnel, un diagnostic ou un traitement.',
      },
      about: {
        title: 'À propos de NeuroScan',
        subtitle: 'Technologie avancée associée à l\'expertise médicale pour révolutionner le diagnostic des tumeurs cérébrales.',
        tryOurTool: 'Essayer notre outil',
        mission: 'Notre mission',
        missionTitle: 'Faire progresser le diagnostic neurologique grâce à l\'IA',
        missionText1: 'NeuroScan a été fondé avec une mission claire : combiner l\'intelligence artificielle de pointe avec l\'expertise neurologique pour fournir un diagnostic plus rapide et plus précis des tumeurs cérébrales.',
        missionText2: 'En créant des outils accessibles pour les professionnels de la santé, nous visons à améliorer les résultats des patients et à réduire le temps entre l\'imagerie, le diagnostic et le traitement.',
        improvingPatientCare: 'Améliorer les soins aux patients',
        reducingDiagnosisTime: 'Réduire le temps de diagnostic',
        technology: 'Notre technologie',
        technologyTitle: 'Classification à la pointe de la technologie',
        technologyText: 'Notre modèle de réseau neuronal a été entraîné sur des milliers d\'IRM et validé par des neuroradiologues experts pour garantir des performances fiables.',
        deepLearning: 'Apprentissage profond',
        deepLearningText: 'Utilisation de réseaux neuronaux convolutifs optimisés pour l\'analyse d\'imagerie médicale avec une précision validée de 98,7%.',
        statisticalAnalysis: 'Analyse statistique',
        statisticalAnalysisText: 'Modèles statistiques avancés pour réduire les faux positifs et fournir des intervalles de confiance pour les résultats de classification.',
        geneticCorrelation: 'Corrélation génétique',
        geneticCorrelationText: 'Cartographie des caractéristiques radiographiques avec des marqueurs génétiques connus pour fournir des indications sur les approches de traitement potentielles.',
        expertise: 'Notre expertise',
        expertiseTitle: 'Collaboration interdisciplinaire',
        expertiseText: 'NeuroScan réunit des experts en neurologie, radiologie, informatique et science des données pour créer une approche véritablement complète.',
        getStarted: 'Commencer',
        getStartedTitle: 'Prêt à découvrir NeuroScan?',
        getStartedText: 'Essayez notre outil de classification maintenant et voyez comment l\'IA peut améliorer votre flux de travail diagnostique.',
        startClassification: 'Commencer la classification',
        requestApiAccess: 'Demander un accès API',
      },
      notFound: {
        title: '404',
        message: 'Oups! Page non trouvée',
        returnHome: 'Retour à l\'accueil',
      },
    }
  },
  pt: {
    translation: {
      common: {
        appName: 'NeuroScan',
        loading: 'Carregando... ',
        error: 'Ocorreu um erro',
        save: 'Salvar',
        cancel: 'Cancelar',
        delete: 'Excluir',
        edit: 'Editar',
        view: 'Visualizar',
        search: 'Pesquisar',
        filter: 'Filtrar',
        filters: 'Filtros',
        advancedFilters: 'Filtros avançados',
        resetFilters: 'Redefinir filtros',
        noResults: 'Nenhum resultado encontrado',
        back: 'Voltar',
        next: 'Próximo',
        previous: 'Anterior',
        submit: 'Enviar',
        reset: 'Redefinir',
        logout: 'Sair',
        login: 'Entrar',
        register: 'Registrar',
        email: 'E-mail',
        password: 'Senha',
        confirmPassword: 'Confirmar senha',
        name: 'Nome',
        role: 'Função',
        admin: 'Administrador',
        doctor: 'Médico',
        darkMode: 'Modo escuro',
        lightMode: 'Modo claro',
        language: 'Idioma',
        english: 'Inglês',
        french: 'Francês',
        portuguese: 'Português',
        footerDescription: 'Classificação avançada de tumores cerebrais usando tecnologia de IA de ponta para auxiliar profissionais médicos no diagnóstico preciso e no planejamento do tratamento.',
        copyright: '© {year} NeuroScan. Todos os direitos reservados. Apenas para fins educacionais.',
        actions: 'Ações',
        export: 'Exportar',
        exportPDF: 'Exportar como PDF',
        exportExcel: 'Exportar como Excel',
        tableView: 'Visualização em tabela',
        cardView: 'Visualização em cartões',
        all: 'Todos',
        male: 'Masculino',
        female: 'Feminino',
        other: 'Outro',
        selectGender: 'Selecionar gênero',
        selectDoctor: 'Selecionar médico',
        selectTumorType: 'Selecionar tipo de tumor',
        min: 'Mín',
        max: 'Máx',
        startDate: 'Data inicial',
        endDate: 'Data final',
        showing: 'Mostrando',
        of: 'de',
        page: 'Página',
        yearsOld: 'anos',
        comingSoon: 'Em breve',
      },
      navigation: {
        home: 'Início',
        about: 'Sobre',
        dashboard: 'Painel',
        patients: 'Pacientes',
        scans: 'Exames',
        users: 'Usuários',
        settings: 'Configurações',
        profile: 'Perfil',
      },
      auth: {
        loginTitle: 'Entrar na sua conta',
        loginSubtitle: 'Digite suas credenciais para acessar sua conta',
        registerTitle: 'Criar uma nova conta',
        registerSubtitle: 'Preencha o formulário para criar sua conta',
        forgotPassword: 'Esqueceu a senha?',
        noAccount: 'Não tem uma conta?',
        hasAccount: 'Já tem uma conta?',
        createAccount: 'Criar conta',
        loginInstead: 'Entrar em vez disso',
        loginSuccess: 'Login bem-sucedido',
        loginError: 'E-mail ou senha inválidos',
        registerSuccess: 'Registro bem-sucedido',
        registerError: 'Falha no registro',
        logoutSuccess: 'Logout bem-sucedido',
        unauthorized: 'Acesso não autorizado',
        unauthorizedMessage: 'Você não tem permissão para acessar esta página',
      },
      dashboard: {
        welcome: 'Bem-vindo',
        recentScans: 'Exames recentes',
        patientCount: 'Total de pacientes',
        scanCount: 'Total de exames',
        pendingScans: 'Exames pendentes',
        completedScans: 'Exames concluídos',
        quickActions: 'Ações rápidas',
        viewAllPatients: 'Ver todos os pacientes',
        addNewPatient: 'Adicionar novo paciente',
        uploadNewScan: 'Enviar novo exame',
        manageUsers: 'Gerenciar usuários',
      },
      patients: {
        patientList: 'Lista de pacientes',
        addPatient: 'Adicionar paciente',
        editPatient: 'Editar paciente',
        deletePatient: 'Excluir paciente',
        patientDetails: 'Detalhes do paciente',
        patientId: 'ID do paciente',
        firstName: 'Nome',
        lastName: 'Sobrenome',
        dateOfBirth: 'Data de nascimento',
        gender: 'Gênero',
        male: 'Masculino',
        female: 'Feminino',
        other: 'Outro',
        contactNumber: 'Telefone',
        address: 'Endereço',
        medicalHistory: 'Histórico médico',
        assignedDoctor: 'Médico responsável',
        noPatients: 'Nenhum paciente encontrado',
        deleteConfirmation: 'Tem certeza de que deseja excluir este paciente?',
        patientCreated: 'Paciente criado com sucesso',
        patientUpdated: 'Paciente atualizado com sucesso',
        patientDeleted: 'Paciente excluído com sucesso',
        patients: 'Pacientes',
        ageRange: 'Faixa etária',
        admissionDate: 'Data de admissão',
        lastVisit: 'Última visita',
        nextAppointment: 'Próxima consulta',
        bloodType: 'Tipo sanguíneo',
        email: 'E-mail',
        height: 'Altura',
        weight: 'Peso',
        emergencyContact: 'Contato de emergência',
        relationship: 'Relacionamento',
        insurance: 'Seguro',
        insuranceInformation: 'Informações do seguro',
        provider: 'Provedor',
        policyNumber: 'Número da apólice',
        expiryDate: 'Data de validade',
        allergies: 'Alergias',
        noAllergies: 'Sem alergias conhecidas',
        chronicConditions: 'Condições crônicas',
        noChronicConditions: 'Sem condições crônicas',
        pastSurgeries: 'Cirurgias anteriores',
        noPastSurgeries: 'Sem cirurgias anteriores',
        familyHistory: 'Histórico familiar',
        noFamilyHistory: 'Sem histórico familiar registrado',
        currentMedications: 'Medicamentos atuais',
        noCurrentMedications: 'Sem medicamentos atuais',
        notes: 'Notas',
        noNotes: 'Sem notas disponíveis',
        timeline: 'Linha do tempo',
        noEvents: 'Nenhum evento registrado',
        medicalInformation: 'Informações médicas',
        patientNotFound: 'Paciente não encontrado',
        patientNotFoundDescription: 'O paciente que você está procurando não existe ou foi removido.',
        noUpcomingAppointments: 'Sem consultas agendadas',
        editPatientDescription: 'Editar informações e histórico médico do paciente.',
      },
      scans: {
        scanList: 'Lista de exames',
        addScan: 'Adicionar exame',
        uploadScan: 'Enviar exame',
        scanDetails: 'Detalhes do exame',
        scanId: 'ID do exame',
        scanDate: 'Data do exame',
        patientScansDescription: 'Visualizar e comparar o histórico de exames do paciente.',
        scanType: 'Tipo de exame',
        scanResult: 'Resultado do exame',
        scanStatus: 'Status do exame',
        pending: 'Pendente',
        processing: 'Processando',
        completed: 'Concluído',
        failed: 'Falhou',
        notes: 'Notas',
        noScans: 'Nenhum exame encontrado',
        uploadInstructions: 'Arraste e solte ou clique para procurar',
        supportedFormats: 'Suporta JPEG, PNG, GIF, BMP, TIFF (Máx 10MB)',
        analyzing: 'Analisando exame cerebral... Por favor, aguarde',
        invalidFileType: 'Envie um arquivo de imagem válido (JPEG, PNG, GIF, BMP, TIFF)',
        fileSizeExceeded: 'O tamanho do arquivo excede o limite de 10MB',
        processingImage: 'Processando imagem...',
        imageReady: 'Imagem pronta para processamento',
        classificationResults: 'Resultados da classificação',
        confidence: 'Pontuação de confiança',
        highConfidence: 'Alta confiança',
        mediumConfidence: 'Confiança média',
        lowConfidence: 'Baixa confiança',
        recommendations: 'Recomendações',
        tumorCharacteristics: 'Características do tumor',
        technicalDetails: 'Detalhes técnicos',
        generateReport: 'Gerar relatório detalhado',
        newScan: 'Novo exame',
      },
      users: {
        userList: 'Lista de usuários',
        addUser: 'Adicionar usuário',
        editUser: 'Editar usuário',
        deleteUser: 'Excluir usuário',
        userDetails: 'Detalhes do usuário',
        userId: 'ID do usuário',
        userName: 'Nome de usuário',
        userEmail: 'E-mail',
        userRole: 'Função',
        userStatus: 'Status',
        active: 'Ativo',
        inactive: 'Inativo',
        suspended: 'Suspenso',
        lastLogin: 'Último acesso',
        createdAt: 'Criado em',
        noUsers: 'Nenhum usuário encontrado',
        deleteConfirmation: 'Tem certeza de que deseja excluir este usuário?',
        userCreated: 'Usuário criado com sucesso',
        userUpdated: 'Usuário atualizado com sucesso',
        userDeleted: 'Usuário excluído com sucesso',
      },
      classification: {
        title: 'Ferramenta de Classificação de Tumores',
        subtitle: 'Envie uma imagem de ressonância magnética para obter resultados instantâneos de classificação com IA, análise detalhada e recomendações.',
        aiPowered: 'Análise com IA',
        neuralNetwork: 'Baseado em rede neural',
        accuracy: '98,7% de precisão',
        geneticCorrelation: 'Correlação genética',
        uploadScan: 'Enviar ressonância',
        results: 'Resultados',
        overview: 'Visão geral',
        details: 'Detalhes',
        size: 'Tamanho',
        location: 'Localização',
        density: 'Densidade',
        borders: 'Bordas',
        model: 'Modelo',
        resolution: 'Resolução',
        scanType: 'Tipo de exame',
        processingTime: 'Tempo de processamento',
        disclaimer: 'Esta ferramenta é destinada apenas para fins educacionais e não deve ser usada como substituto de aconselhamento, diagnóstico ou tratamento médico profissional.',
      },
      about: {
        title: 'Sobre o NeuroScan',
        subtitle: 'Tecnologia avançada aliada à expertise médica para revolucionar o diagnóstico de tumores cerebrais.',
        tryOurTool: 'Experimente nossa ferramenta',
        mission: 'Nossa missão',
        missionTitle: 'Avançando o diagnóstico neurológico através da IA',
        missionText1: 'O NeuroScan foi fundado com uma missão clara: combinar inteligência artificial de ponta com expertise neurológica para fornecer diagnósticos mais rápidos e precisos de tumores cerebrais.',
        missionText2: 'Ao criar ferramentas acessíveis para profissionais de saúde, nosso objetivo é melhorar os resultados dos pacientes e reduzir o tempo entre imagem, diagnóstico e tratamento.',
        improvingPatientCare: 'Melhorando o cuidado ao paciente',
        reducingDiagnosisTime: 'Reduzindo o tempo de diagnóstico',
        technology: 'Nossa tecnologia',
        technologyTitle: 'Classificação de última geração',
        technologyText: 'Nosso modelo de rede neural foi treinado em milhares de exames de ressonância magnética e validado por especialistas para garantir desempenho confiável.',
        deepLearning: 'Aprendizado profundo',
        deepLearningText: 'Utilizando redes neurais convolucionais otimizadas para análise de imagens médicas com 98,7% de precisão validada.',
        statisticalAnalysis: 'Análise estatística',
        statisticalAnalysisText: 'Modelos estatísticos avançados para reduzir falsos positivos e fornecer intervalos de confiança para os resultados da classificação.',
        geneticCorrelation: 'Correlação genética',
        geneticCorrelationText: 'Mapeando características radiográficas para marcadores genéticos conhecidos e fornecendo insights para possíveis abordagens de tratamento.',
        expertise: 'Nossa expertise',
        expertiseTitle: 'Colaboração interdisciplinar',
        expertiseText: 'O NeuroScan reúne especialistas em neurologia, radiologia, ciência da computação e ciência de dados para criar uma abordagem verdadeiramente abrangente.',
        getStarted: 'Começar',
        getStartedTitle: 'Pronto para experimentar o NeuroScan?',
        getStartedText: 'Experimente nossa ferramenta de classificação agora e veja como a IA pode aprimorar seu fluxo de trabalho diagnóstico.',
        startClassification: 'Iniciar classificação',
        requestApiAccess: 'Solicitar acesso à API',
      },
      notFound: {
        title: '404',
        message: 'Ops! Página não encontrada',
        returnHome: 'Voltar para o início',
      },
    }
  }
};

// Initialize i18n
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: true, // Always enable debug
    interpolation: {
      escapeValue: false, // React already escapes values
    },
  });

// Log the current language and translations
console.log('Current language:', i18n.language);
console.log('Has translations:', i18n.hasResourceBundle(i18n.language, 'translation'));

export default i18n;

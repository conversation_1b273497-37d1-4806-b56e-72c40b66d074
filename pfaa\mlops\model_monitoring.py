"""
MLOps - Model Monitoring
Surveillance et monitoring des modèles en production
"""

import json
import time
import psutil
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class ModelMonitor:
    """
    Système de monitoring pour les modèles ML en production
    """
    
    def __init__(self, monitoring_path: str = "mlops/monitoring"):
        self.monitoring_path = Path(monitoring_path)
        self.monitoring_path.mkdir(parents=True, exist_ok=True)
        
        # Métriques en temps réel
        self.metrics_buffer = defaultdict(lambda: deque(maxlen=1000))
        self.alerts = []
        
        # Configuration des seuils d'alerte
        self.alert_thresholds = {
            "inference_time_ms": 5000,  # 5 secondes max
            "memory_usage_mb": 1000,    # 1GB max
            "cpu_usage_percent": 80,    # 80% max
            "error_rate_percent": 5,    # 5% max
            "confidence_threshold": 0.7  # Confiance minimum
        }
        
        # Historique des prédictions
        self.prediction_history = deque(maxlen=10000)
        
    def log_prediction(
        self,
        model_id: str,
        input_data: Dict,
        prediction: Dict,
        inference_time_ms: float,
        confidence: float = None
    ):
        """
        Enregistre une prédiction pour le monitoring
        
        Args:
            model_id: ID du modèle utilisé
            input_data: Données d'entrée (métadonnées)
            prediction: Résultat de la prédiction
            inference_time_ms: Temps d'inférence en millisecondes
            confidence: Score de confiance
        """
        timestamp = datetime.now()
        
        prediction_log = {
            "timestamp": timestamp.isoformat(),
            "model_id": model_id,
            "input_metadata": {
                "image_size": input_data.get("image_size"),
                "file_format": input_data.get("file_format"),
                "file_size_mb": input_data.get("file_size_mb")
            },
            "prediction": {
                "tumor_type": prediction.get("tumorType"),
                "confidence": confidence or prediction.get("confidence", 0),
                "processing_time_ms": inference_time_ms
            },
            "system_metrics": self._get_system_metrics(),
            "success": True
        }
        
        # Ajout à l'historique
        self.prediction_history.append(prediction_log)
        
        # Mise à jour des métriques en temps réel
        self._update_real_time_metrics(prediction_log)
        
        # Vérification des alertes
        self._check_alerts(prediction_log)
        
        # Sauvegarde périodique
        self._save_metrics_to_file()
    
    def log_error(
        self,
        model_id: str,
        error_type: str,
        error_message: str,
        input_data: Dict = None
    ):
        """Enregistre une erreur de prédiction"""
        timestamp = datetime.now()
        
        error_log = {
            "timestamp": timestamp.isoformat(),
            "model_id": model_id,
            "error_type": error_type,
            "error_message": error_message,
            "input_metadata": input_data or {},
            "system_metrics": self._get_system_metrics(),
            "success": False
        }
        
        self.prediction_history.append(error_log)
        self._update_real_time_metrics(error_log)
        self._check_alerts(error_log)
        
        logger.error(f"Erreur modèle {model_id}: {error_type} - {error_message}")
    
    def _get_system_metrics(self) -> Dict:
        """Récupère les métriques système actuelles"""
        try:
            return {
                "cpu_percent": psutil.cpu_percent(interval=0.1),
                "memory_percent": psutil.virtual_memory().percent,
                "memory_used_mb": psutil.virtual_memory().used / (1024 * 1024),
                "disk_usage_percent": psutil.disk_usage('/').percent,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des métriques système: {e}")
            return {}
    
    def _update_real_time_metrics(self, log_entry: Dict):
        """Met à jour les métriques en temps réel"""
        timestamp = datetime.now()
        
        # Métriques de performance
        if log_entry.get("success"):
            processing_time = log_entry["prediction"]["processing_time_ms"]
            confidence = log_entry["prediction"]["confidence"]
            
            self.metrics_buffer["inference_times"].append({
                "timestamp": timestamp,
                "value": processing_time
            })
            
            self.metrics_buffer["confidences"].append({
                "timestamp": timestamp,
                "value": confidence
            })
        
        # Métriques système
        system_metrics = log_entry.get("system_metrics", {})
        if system_metrics:
            self.metrics_buffer["cpu_usage"].append({
                "timestamp": timestamp,
                "value": system_metrics.get("cpu_percent", 0)
            })
            
            self.metrics_buffer["memory_usage"].append({
                "timestamp": timestamp,
                "value": system_metrics.get("memory_used_mb", 0)
            })
        
        # Taux d'erreur
        self.metrics_buffer["predictions_total"].append({
            "timestamp": timestamp,
            "success": log_entry.get("success", False)
        })
    
    def _check_alerts(self, log_entry: Dict):
        """Vérifie et génère des alertes si nécessaire"""
        timestamp = datetime.now()
        
        # Alerte temps d'inférence
        if log_entry.get("success"):
            processing_time = log_entry["prediction"]["processing_time_ms"]
            if processing_time > self.alert_thresholds["inference_time_ms"]:
                self._create_alert(
                    "HIGH_INFERENCE_TIME",
                    f"Temps d'inférence élevé: {processing_time:.2f}ms",
                    {"processing_time_ms": processing_time}
                )
        
        # Alerte confiance faible
        if log_entry.get("success"):
            confidence = log_entry["prediction"]["confidence"]
            if confidence < self.alert_thresholds["confidence_threshold"]:
                self._create_alert(
                    "LOW_CONFIDENCE",
                    f"Confiance faible: {confidence:.2f}",
                    {"confidence": confidence}
                )
        
        # Alerte utilisation système
        system_metrics = log_entry.get("system_metrics", {})
        if system_metrics:
            cpu_usage = system_metrics.get("cpu_percent", 0)
            memory_usage = system_metrics.get("memory_used_mb", 0)
            
            if cpu_usage > self.alert_thresholds["cpu_usage_percent"]:
                self._create_alert(
                    "HIGH_CPU_USAGE",
                    f"Utilisation CPU élevée: {cpu_usage:.1f}%",
                    {"cpu_percent": cpu_usage}
                )
            
            if memory_usage > self.alert_thresholds["memory_usage_mb"]:
                self._create_alert(
                    "HIGH_MEMORY_USAGE",
                    f"Utilisation mémoire élevée: {memory_usage:.1f}MB",
                    {"memory_mb": memory_usage}
                )
        
        # Alerte taux d'erreur
        error_rate = self._calculate_error_rate()
        if error_rate > self.alert_thresholds["error_rate_percent"]:
            self._create_alert(
                "HIGH_ERROR_RATE",
                f"Taux d'erreur élevé: {error_rate:.1f}%",
                {"error_rate_percent": error_rate}
            )
    
    def _create_alert(self, alert_type: str, message: str, metadata: Dict):
        """Crée une nouvelle alerte"""
        alert = {
            "timestamp": datetime.now().isoformat(),
            "type": alert_type,
            "message": message,
            "metadata": metadata,
            "severity": self._get_alert_severity(alert_type),
            "resolved": False
        }
        
        self.alerts.append(alert)
        logger.warning(f"ALERTE {alert_type}: {message}")
        
        # Garder seulement les 100 dernières alertes
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def _get_alert_severity(self, alert_type: str) -> str:
        """Détermine la sévérité d'une alerte"""
        high_severity = ["HIGH_ERROR_RATE", "HIGH_MEMORY_USAGE"]
        medium_severity = ["HIGH_INFERENCE_TIME", "HIGH_CPU_USAGE"]
        
        if alert_type in high_severity:
            return "HIGH"
        elif alert_type in medium_severity:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _calculate_error_rate(self, window_minutes: int = 10) -> float:
        """Calcule le taux d'erreur sur une fenêtre de temps"""
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        
        recent_predictions = [
            p for p in self.prediction_history
            if datetime.fromisoformat(p["timestamp"]) > cutoff_time
        ]
        
        if not recent_predictions:
            return 0.0
        
        error_count = sum(1 for p in recent_predictions if not p.get("success", True))
        return (error_count / len(recent_predictions)) * 100
    
    def get_metrics_summary(self, hours: int = 24) -> Dict:
        """Génère un résumé des métriques sur une période donnée"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_predictions = [
            p for p in self.prediction_history
            if datetime.fromisoformat(p["timestamp"]) > cutoff_time
            and p.get("success", False)
        ]
        
        if not recent_predictions:
            return {"message": "Aucune prédiction dans la période spécifiée"}
        
        # Calculs des métriques
        inference_times = [p["prediction"]["processing_time_ms"] for p in recent_predictions]
        confidences = [p["prediction"]["confidence"] for p in recent_predictions]
        
        summary = {
            "period_hours": hours,
            "total_predictions": len(recent_predictions),
            "error_rate_percent": self._calculate_error_rate(hours * 60),
            "inference_time": {
                "avg_ms": np.mean(inference_times),
                "min_ms": np.min(inference_times),
                "max_ms": np.max(inference_times),
                "p95_ms": np.percentile(inference_times, 95),
                "p99_ms": np.percentile(inference_times, 99)
            },
            "confidence": {
                "avg": np.mean(confidences),
                "min": np.min(confidences),
                "max": np.max(confidences),
                "std": np.std(confidences)
            },
            "predictions_per_hour": len(recent_predictions) / hours,
            "active_alerts": len([a for a in self.alerts if not a.get("resolved", False)])
        }
        
        return summary
    
    def get_active_alerts(self) -> List[Dict]:
        """Récupère les alertes actives"""
        return [alert for alert in self.alerts if not alert.get("resolved", False)]
    
    def resolve_alert(self, alert_index: int) -> bool:
        """Marque une alerte comme résolue"""
        try:
            if 0 <= alert_index < len(self.alerts):
                self.alerts[alert_index]["resolved"] = True
                self.alerts[alert_index]["resolved_at"] = datetime.now().isoformat()
                return True
            return False
        except Exception as e:
            logger.error(f"Erreur lors de la résolution de l'alerte: {e}")
            return False
    
    def _save_metrics_to_file(self):
        """Sauvegarde périodique des métriques"""
        try:
            # Sauvegarde toutes les 100 prédictions
            if len(self.prediction_history) % 100 == 0:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                metrics_file = self.monitoring_path / f"metrics_{timestamp}.json"
                
                metrics_data = {
                    "timestamp": datetime.now().isoformat(),
                    "summary": self.get_metrics_summary(),
                    "recent_predictions": list(self.prediction_history)[-100:],
                    "active_alerts": self.get_active_alerts()
                }
                
                with open(metrics_file, 'w') as f:
                    json.dump(metrics_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des métriques: {e}")
    
    def export_metrics(self, start_date: str = None, end_date: str = None) -> Dict:
        """Exporte les métriques pour une période donnée"""
        try:
            filtered_history = list(self.prediction_history)
            
            if start_date:
                start_dt = datetime.fromisoformat(start_date)
                filtered_history = [
                    p for p in filtered_history
                    if datetime.fromisoformat(p["timestamp"]) >= start_dt
                ]
            
            if end_date:
                end_dt = datetime.fromisoformat(end_date)
                filtered_history = [
                    p for p in filtered_history
                    if datetime.fromisoformat(p["timestamp"]) <= end_dt
                ]
            
            return {
                "export_timestamp": datetime.now().isoformat(),
                "period": {"start": start_date, "end": end_date},
                "total_records": len(filtered_history),
                "predictions": filtered_history,
                "alerts": self.alerts
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de l'export des métriques: {e}")
            return {"error": str(e)}

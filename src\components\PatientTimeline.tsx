import React from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Calendar, 
  Clock, 
  Pill, 
  Stethoscope, 
  FileText, 
  Scissors, 
  Radiation,
  CheckCircle,
  Clock3,
  X<PERSON>ircle,
  AlertCircle
} from 'lucide-react';
import { 
  Appointment, 
  Scan, 
  Treatment 
} from '@/data/mockPatients';

interface TimelineEvent {
  id: string;
  date: string;
  time?: string;
  type: 'appointment' | 'scan' | 'treatment';
  title: string;
  description: string;
  status: string;
  icon: React.ReactNode;
  color: string;
}

interface PatientTimelineProps {
  appointments: Appointment[];
  scans: Scan[];
  treatments: Treatment[];
}

const PatientTimeline: React.FC<PatientTimelineProps> = ({ 
  appointments, 
  scans, 
  treatments 
}) => {
  const { t } = useTranslation();

  // Convert appointments to timeline events
  const appointmentEvents: TimelineEvent[] = appointments.map(appointment => ({
    id: appointment.id,
    date: appointment.date,
    time: appointment.time,
    type: 'appointment',
    title: appointment.purpose,
    description: appointment.notes,
    status: appointment.status,
    icon: <Stethoscope size={16} />,
    color: getStatusColor(appointment.status)
  }));

  // Convert scans to timeline events
  const scanEvents: TimelineEvent[] = scans.map(scan => ({
    id: scan.id,
    date: scan.date,
    type: 'scan',
    title: `${scan.type} - ${scan.bodyPart}`,
    description: scan.result.notes,
    status: scan.status,
    icon: <FileText size={16} />,
    color: getStatusColor(scan.status)
  }));

  // Convert treatments to timeline events
  const treatmentEvents: TimelineEvent[] = treatments.map(treatment => {
    let icon;
    switch (treatment.type) {
      case 'Medication':
        icon = <Pill size={16} />;
        break;
      case 'Surgery':
        icon = <Scissors size={16} />;
        break;
      case 'Radiation':
        icon = <Radiation size={16} />;
        break;
      default:
        icon = <Pill size={16} />;
    }

    return {
      id: treatment.id,
      date: treatment.startDate,
      type: 'treatment',
      title: `${treatment.type} - ${treatment.name}`,
      description: treatment.notes,
      status: treatment.status,
      icon,
      color: getStatusColor(treatment.status)
    };
  });

  // Combine all events and sort by date (most recent first)
  const allEvents = [...appointmentEvents, ...scanEvents, ...treatmentEvents]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // Helper function to get status color
  function getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'text-green-500';
      case 'scheduled':
        return 'text-blue-500';
      case 'active':
        return 'text-blue-500';
      case 'pending':
        return 'text-amber-500';
      case 'processing':
        return 'text-amber-500';
      case 'cancelled':
        return 'text-red-500';
      case 'failed':
        return 'text-red-500';
      case 'no-show':
        return 'text-red-500';
      default:
        return 'text-slate-500';
    }
  }

  // Helper function to get status icon
  function getStatusIcon(status: string) {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'scheduled':
        return <Calendar className="h-4 w-4 text-blue-500" />;
      case 'active':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <Clock3 className="h-4 w-4 text-amber-500" />;
      case 'processing':
        return <Clock3 className="h-4 w-4 text-amber-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'no-show':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-slate-500" />;
    }
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{t('patients.timeline')}</h3>
      
      {allEvents.length === 0 ? (
        <p className="text-muted-foreground">{t('patients.noEvents')}</p>
      ) : (
        <div className="space-y-4">
          {allEvents.map((event, index) => (
            <div key={event.id} className="relative pl-6 pb-4">
              {/* Timeline connector */}
              {index < allEvents.length - 1 && (
                <div className="absolute left-2.5 top-3 bottom-0 w-px bg-slate-200 dark:bg-slate-700"></div>
              )}
              
              {/* Timeline dot */}
              <div className={`absolute left-0 top-1.5 h-5 w-5 rounded-full border-2 border-white dark:border-slate-800 flex items-center justify-center ${event.color} bg-white dark:bg-slate-800`}>
                {event.icon}
              </div>
              
              {/* Event content */}
              <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3 shadow-sm">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-slate-900 dark:text-white">{event.title}</h4>
                  <div className="flex items-center gap-1 text-xs">
                    {getStatusIcon(event.status)}
                    <span className={getStatusColor(event.status)}>
                      {event.status}
                    </span>
                  </div>
                </div>
                
                <div className="text-sm text-slate-500 dark:text-slate-400 mb-2">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3.5 w-3.5" />
                    <span>{format(new Date(event.date), 'PPP')}</span>
                    {event.time && (
                      <>
                        <span className="mx-1">•</span>
                        <Clock className="h-3.5 w-3.5" />
                        <span>{event.time}</span>
                      </>
                    )}
                  </div>
                </div>
                
                <p className="text-sm text-slate-600 dark:text-slate-300">
                  {event.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PatientTimeline;

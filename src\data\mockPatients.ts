import { 
  <PERSON>, 
  <PERSON>ll, 
  Syringe, 
  Radiation, 
  Scissors, 
  Stethoscope,
  Activity
} from 'lucide-react';

export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female' | 'Other';
  contactNumber: string;
  email: string;
  address: string;
  bloodType: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-';
  height: number; // in cm
  weight: number; // in kg
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  insurance: {
    provider: string;
    policyNumber: string;
    expiryDate: string;
  };
  doctor: string;
  medicalHistory: {
    allergies: string[];
    chronicConditions: string[];
    pastSurgeries: {
      procedure: string;
      date: string;
      notes: string;
    }[];
    familyHistory: string[];
  };
  lastScan: string;
  lastVisit: string;
  nextAppointment: string | null;
  notes: string;
}

export interface Scan {
  id: string;
  patientId: string;
  date: string;
  type: 'MRI' | 'CT' | 'PET' | 'X-Ray';
  bodyPart: string;
  imageUrl: string;
  result: {
    diagnosis: string;
    tumorType?: string;
    tumorSize?: string;
    tumorLocation?: string;
    malignant?: boolean;
    notes: string;
  };
  doctor: string;
  facility: string;
  status: 'Completed' | 'Pending' | 'Processing' | 'Failed';
}

export interface Treatment {
  id: string;
  patientId: string;
  type: 'Medication' | 'Surgery' | 'Radiation' | 'Chemotherapy' | 'Physical Therapy' | 'Other';
  name: string;
  startDate: string;
  endDate: string | null;
  frequency?: string;
  dosage?: string;
  doctor: string;
  notes: string;
  status: 'Active' | 'Completed' | 'Scheduled' | 'Cancelled';
  sideEffects: string[];
  effectiveness: 'Excellent' | 'Good' | 'Moderate' | 'Poor' | 'Unknown';
  icon: any;
}

export interface Appointment {
  id: string;
  patientId: string;
  date: string;
  time: string;
  doctor: string;
  purpose: string;
  notes: string;
  status: 'Scheduled' | 'Completed' | 'Cancelled' | 'No-Show';
  followUp: boolean;
}

export const mockPatients: Patient[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: '1980-05-15',
    gender: 'Male',
    contactNumber: '+****************',
    email: '<EMAIL>',
    address: '123 Main St, Boston, MA 02108',
    bloodType: 'A+',
    height: 180,
    weight: 75,
    emergencyContact: {
      name: 'Jane Doe',
      relationship: 'Spouse',
      phone: '+****************',
    },
    insurance: {
      provider: 'Blue Cross',
      policyNumber: 'BC-12345678',
      expiryDate: '2024-12-31',
    },
    doctor: 'Dr. Sarah Johnson',
    medicalHistory: {
      allergies: ['Penicillin', 'Peanuts'],
      chronicConditions: ['Hypertension'],
      pastSurgeries: [
        {
          procedure: 'Appendectomy',
          date: '2010-03-22',
          notes: 'No complications',
        },
      ],
      familyHistory: ['Diabetes (Father)', 'Breast Cancer (Mother)'],
    },
    lastScan: '2023-10-12',
    lastVisit: '2023-11-05',
    nextAppointment: '2023-12-15',
    notes: 'Patient has been responding well to current treatment plan.',
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    dateOfBirth: '1975-08-22',
    gender: 'Female',
    contactNumber: '+****************',
    email: '<EMAIL>',
    address: '456 Oak Ave, Cambridge, MA 02139',
    bloodType: 'O-',
    height: 165,
    weight: 62,
    emergencyContact: {
      name: 'Robert Smith',
      relationship: 'Husband',
      phone: '+****************',
    },
    insurance: {
      provider: 'Aetna',
      policyNumber: 'AE-87654321',
      expiryDate: '2024-06-30',
    },
    doctor: 'Dr. Michael Chen',
    medicalHistory: {
      allergies: ['Sulfa drugs'],
      chronicConditions: ['Asthma', 'Migraines'],
      pastSurgeries: [
        {
          procedure: 'Cesarean section',
          date: '2005-07-10',
          notes: 'Delivery of twins',
        },
      ],
      familyHistory: ['Heart disease (Father)', 'Stroke (Grandfather)'],
    },
    lastScan: '2023-11-05',
    lastVisit: '2023-11-20',
    nextAppointment: '2024-01-10',
    notes: 'Patient has reported increased frequency of migraines.',
  },
  {
    id: '3',
    firstName: 'Robert',
    lastName: 'Williams',
    dateOfBirth: '1990-03-10',
    gender: 'Male',
    contactNumber: '+****************',
    email: '<EMAIL>',
    address: '789 Pine St, Brookline, MA 02445',
    bloodType: 'B+',
    height: 175,
    weight: 70,
    emergencyContact: {
      name: 'Mary Williams',
      relationship: 'Mother',
      phone: '+****************',
    },
    insurance: {
      provider: 'Cigna',
      policyNumber: 'CI-23456789',
      expiryDate: '2024-09-30',
    },
    doctor: 'Dr. Sarah Johnson',
    medicalHistory: {
      allergies: [],
      chronicConditions: ['Anxiety'],
      pastSurgeries: [],
      familyHistory: ['Alzheimer\'s (Grandmother)'],
    },
    lastScan: '2023-09-28',
    lastVisit: '2023-10-15',
    nextAppointment: '2023-12-20',
    notes: 'Patient is currently participating in clinical trial for new anxiety medication.',
  },
  {
    id: '4',
    firstName: 'Emily',
    lastName: 'Johnson',
    dateOfBirth: '1988-12-03',
    gender: 'Female',
    contactNumber: '+****************',
    email: '<EMAIL>',
    address: '101 Elm St, Somerville, MA 02143',
    bloodType: 'AB+',
    height: 170,
    weight: 65,
    emergencyContact: {
      name: 'David Johnson',
      relationship: 'Brother',
      phone: '+****************',
    },
    insurance: {
      provider: 'United Healthcare',
      policyNumber: 'UH-34567890',
      expiryDate: '2024-03-31',
    },
    doctor: 'Dr. Michael Chen',
    medicalHistory: {
      allergies: ['Latex'],
      chronicConditions: ['Hypothyroidism'],
      pastSurgeries: [
        {
          procedure: 'Tonsillectomy',
          date: '1998-06-15',
          notes: 'Routine procedure',
        },
      ],
      familyHistory: ['Thyroid disorders (Mother, Sister)'],
    },
    lastScan: '2023-10-30',
    lastVisit: '2023-11-10',
    nextAppointment: '2024-02-05',
    notes: 'Thyroid levels stable with current medication.',
  },
  {
    id: '5',
    firstName: 'Michael',
    lastName: 'Brown',
    dateOfBirth: '1965-07-19',
    gender: 'Male',
    contactNumber: '+****************',
    email: '<EMAIL>',
    address: '222 Maple Ave, Newton, MA 02458',
    bloodType: 'A-',
    height: 182,
    weight: 88,
    emergencyContact: {
      name: 'Susan Brown',
      relationship: 'Wife',
      phone: '+****************',
    },
    insurance: {
      provider: 'Medicare',
      policyNumber: 'MC-45678901',
      expiryDate: '2024-12-31',
    },
    doctor: 'Dr. Sarah Johnson',
    medicalHistory: {
      allergies: ['Codeine'],
      chronicConditions: ['Type 2 Diabetes', 'Hypertension'],
      pastSurgeries: [
        {
          procedure: 'Knee replacement',
          date: '2018-09-05',
          notes: 'Right knee',
        },
      ],
      familyHistory: ['Heart disease (Father, Brother)', 'Diabetes (Mother)'],
    },
    lastScan: '2023-11-15',
    lastVisit: '2023-11-25',
    nextAppointment: '2023-12-30',
    notes: 'Blood sugar levels have improved with dietary changes.',
  },
];

export const mockScans: Scan[] = [
  {
    id: 'scan-1',
    patientId: '1',
    date: '2023-10-12',
    type: 'MRI',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-**********-7cb056fba93d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Glioblastoma',
      tumorType: 'Malignant',
      tumorSize: '3.2 cm',
      tumorLocation: 'Right frontal lobe',
      malignant: true,
      notes: 'Aggressive tumor with surrounding edema. Recommend immediate treatment.',
    },
    doctor: 'Dr. Sarah Johnson',
    facility: 'Boston Medical Center',
    status: 'Completed',
  },
  {
    id: 'scan-2',
    patientId: '1',
    date: '2023-08-05',
    type: 'CT',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-1530026186672-2cd00ffc50fe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Glioblastoma',
      tumorType: 'Malignant',
      tumorSize: '2.8 cm',
      tumorLocation: 'Right frontal lobe',
      malignant: true,
      notes: 'Initial detection of tumor. Recommend MRI for further evaluation.',
    },
    doctor: 'Dr. Sarah Johnson',
    facility: 'Boston Medical Center',
    status: 'Completed',
  },
  {
    id: 'scan-3',
    patientId: '1',
    date: '2023-11-20',
    type: 'MRI',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-1583911860205-72f8ac8ddcbe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Glioblastoma - post treatment',
      tumorType: 'Malignant',
      tumorSize: '2.5 cm',
      tumorLocation: 'Right frontal lobe',
      malignant: true,
      notes: 'Slight reduction in tumor size following initial treatment. Continue current treatment plan.',
    },
    doctor: 'Dr. Sarah Johnson',
    facility: 'Boston Medical Center',
    status: 'Completed',
  },
  {
    id: 'scan-4',
    patientId: '2',
    date: '2023-11-05',
    type: 'MRI',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Meningioma',
      tumorType: 'Benign',
      tumorSize: '1.5 cm',
      tumorLocation: 'Left temporal lobe',
      malignant: false,
      notes: 'Small benign tumor. Recommend monitoring with follow-up scan in 3 months.',
    },
    doctor: 'Dr. Michael Chen',
    facility: 'Massachusetts General Hospital',
    status: 'Completed',
  },
  {
    id: 'scan-5',
    patientId: '3',
    date: '2023-09-28',
    type: 'CT',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-**********-0ce5c515b610?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Normal',
      notes: 'No abnormalities detected.',
    },
    doctor: 'Dr. Sarah Johnson',
    facility: 'Boston Medical Center',
    status: 'Completed',
  },
  {
    id: 'scan-6',
    patientId: '4',
    date: '2023-10-30',
    type: 'MRI',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-**********-7a21d89e74aa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Acoustic Neuroma',
      tumorType: 'Benign',
      tumorSize: '0.8 cm',
      tumorLocation: 'Right acoustic nerve',
      malignant: false,
      notes: 'Small benign tumor on acoustic nerve. May explain reported hearing issues.',
    },
    doctor: 'Dr. Michael Chen',
    facility: 'Massachusetts General Hospital',
    status: 'Completed',
  },
  {
    id: 'scan-7',
    patientId: '5',
    date: '2023-11-15',
    type: 'MRI',
    bodyPart: 'Brain',
    imageUrl: 'https://images.unsplash.com/photo-**********-49f3e32cddf9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    result: {
      diagnosis: 'Pituitary Adenoma',
      tumorType: 'Benign',
      tumorSize: '1.2 cm',
      tumorLocation: 'Pituitary gland',
      malignant: false,
      notes: 'Benign pituitary tumor. Recommend endocrinology consult to assess hormonal impact.',
    },
    doctor: 'Dr. Sarah Johnson',
    facility: 'Boston Medical Center',
    status: 'Completed',
  },
];

export const mockTreatments: Treatment[] = [
  {
    id: 'treatment-1',
    patientId: '1',
    type: 'Medication',
    name: 'Temozolomide',
    startDate: '2023-10-15',
    endDate: null,
    frequency: 'Daily',
    dosage: '150mg',
    doctor: 'Dr. Sarah Johnson',
    notes: 'Chemotherapy medication for glioblastoma treatment.',
    status: 'Active',
    sideEffects: ['Nausea', 'Fatigue', 'Decreased appetite'],
    effectiveness: 'Moderate',
    icon: Pill,
  },
  {
    id: 'treatment-2',
    patientId: '1',
    type: 'Radiation',
    name: 'External Beam Radiation Therapy',
    startDate: '2023-10-20',
    endDate: '2023-11-30',
    frequency: '5 days per week',
    doctor: 'Dr. James Wilson',
    notes: 'Targeted radiation therapy to reduce tumor size.',
    status: 'Active',
    sideEffects: ['Skin irritation', 'Fatigue', 'Hair loss'],
    effectiveness: 'Good',
    icon: Radiation,
  },
  {
    id: 'treatment-3',
    patientId: '1',
    type: 'Surgery',
    name: 'Craniotomy for Tumor Resection',
    startDate: '2023-09-05',
    endDate: '2023-09-05',
    doctor: 'Dr. Emily Rodriguez',
    notes: 'Partial tumor removal surgery. Approximately 70% of tumor mass removed.',
    status: 'Completed',
    sideEffects: ['Post-operative pain', 'Temporary speech difficulties'],
    effectiveness: 'Good',
    icon: Scissors,
  },
  {
    id: 'treatment-4',
    patientId: '2',
    type: 'Medication',
    name: 'Dexamethasone',
    startDate: '2023-11-10',
    endDate: null,
    frequency: 'Twice daily',
    dosage: '4mg',
    doctor: 'Dr. Michael Chen',
    notes: 'Corticosteroid to reduce inflammation and pressure.',
    status: 'Active',
    sideEffects: ['Increased appetite', 'Mood changes', 'Insomnia'],
    effectiveness: 'Good',
    icon: Pill,
  },
  {
    id: 'treatment-5',
    patientId: '4',
    type: 'Medication',
    name: 'Propranolol',
    startDate: '2023-11-05',
    endDate: null,
    frequency: 'Daily',
    dosage: '40mg',
    doctor: 'Dr. Michael Chen',
    notes: 'Beta-blocker to manage symptoms related to acoustic neuroma.',
    status: 'Active',
    sideEffects: ['Fatigue', 'Dizziness'],
    effectiveness: 'Moderate',
    icon: Pill,
  },
  {
    id: 'treatment-6',
    patientId: '5',
    type: 'Medication',
    name: 'Cabergoline',
    startDate: '2023-11-20',
    endDate: null,
    frequency: 'Twice weekly',
    dosage: '0.5mg',
    doctor: 'Dr. Sarah Johnson',
    notes: 'Dopamine agonist to manage pituitary adenoma.',
    status: 'Active',
    sideEffects: ['Nausea', 'Dizziness'],
    effectiveness: 'Good',
    icon: Pill,
  },
  {
    id: 'treatment-7',
    patientId: '5',
    type: 'Surgery',
    name: 'Transsphenoidal Surgery',
    startDate: '2024-01-15',
    endDate: null,
    doctor: 'Dr. Emily Rodriguez',
    notes: 'Scheduled surgery to remove pituitary adenoma.',
    status: 'Scheduled',
    sideEffects: [],
    effectiveness: 'Unknown',
    icon: Scissors,
  },
];

export const mockAppointments: Appointment[] = [
  {
    id: 'appointment-1',
    patientId: '1',
    date: '2023-12-15',
    time: '10:00 AM',
    doctor: 'Dr. Sarah Johnson',
    purpose: 'Follow-up after radiation therapy',
    notes: 'Assess treatment response and side effects management.',
    status: 'Scheduled',
    followUp: true,
  },
  {
    id: 'appointment-2',
    patientId: '1',
    date: '2023-11-05',
    time: '2:30 PM',
    doctor: 'Dr. Sarah Johnson',
    purpose: 'Treatment planning',
    notes: 'Discussed radiation therapy schedule and medication adjustments.',
    status: 'Completed',
    followUp: true,
  },
  {
    id: 'appointment-3',
    patientId: '2',
    date: '2024-01-10',
    time: '9:15 AM',
    doctor: 'Dr. Michael Chen',
    purpose: 'Follow-up MRI review',
    notes: 'Review latest scan results and adjust treatment plan if necessary.',
    status: 'Scheduled',
    followUp: true,
  },
  {
    id: 'appointment-4',
    patientId: '3',
    date: '2023-12-20',
    time: '11:30 AM',
    doctor: 'Dr. Sarah Johnson',
    purpose: 'Routine check-up',
    notes: 'General health assessment and medication review.',
    status: 'Scheduled',
    followUp: false,
  },
  {
    id: 'appointment-5',
    patientId: '4',
    date: '2024-02-05',
    time: '3:00 PM',
    doctor: 'Dr. Michael Chen',
    purpose: 'Follow-up scan',
    notes: 'Schedule follow-up MRI to monitor acoustic neuroma.',
    status: 'Scheduled',
    followUp: true,
  },
  {
    id: 'appointment-6',
    patientId: '5',
    date: '2023-12-30',
    time: '10:45 AM',
    doctor: 'Dr. Sarah Johnson',
    purpose: 'Pre-surgical consultation',
    notes: 'Prepare for upcoming pituitary surgery and discuss expectations.',
    status: 'Scheduled',
    followUp: false,
  },
];

// Helper function to get patient by ID
export const getPatientById = (id: string): Patient | undefined => {
  return mockPatients.find(patient => patient.id === id);
};

// Helper function to get scans by patient ID
export const getScansByPatientId = (patientId: string): Scan[] => {
  return mockScans.filter(scan => scan.patientId === patientId);
};

// Helper function to get treatments by patient ID
export const getTreatmentsByPatientId = (patientId: string): Treatment[] => {
  return mockTreatments.filter(treatment => treatment.patientId === patientId);
};

// Helper function to get appointments by patient ID
export const getAppointmentsByPatientId = (patientId: string): Appointment[] => {
  return mockAppointments.filter(appointment => appointment.patientId === patientId);
};

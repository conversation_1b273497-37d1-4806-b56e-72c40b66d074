import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, 
  FileText, 
  Calendar, 
  Pill, 
  Activity, 
  Plus,
  Clipboard,
  History,
  Brain
} from 'lucide-react';
import { 
  getPatientById, 
  getTreatmentsByPatientId, 
  getAppointmentsByPatientId,
  Patient, 
  Treatment,
  Appointment
} from '@/data/mockPatients';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import TreatmentPlan from '@/components/TreatmentPlan';
import TreatmentHistory from '@/components/TreatmentHistory';
import AppointmentCalendar from '@/components/AppointmentCalendar';
import MedicationTracking from '@/components/MedicationTracking';

const PatientTreatmentTracking: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [patient, setPatient] = useState<Patient | null>(null);
  const [treatments, setTreatments] = useState<Treatment[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('plan');
  
  useEffect(() => {
    if (id) {
      const patientData = getPatientById(id);
      if (patientData) {
        setPatient(patientData);
        const patientTreatments = getTreatmentsByPatientId(id);
        setTreatments(patientTreatments);
        const patientAppointments = getAppointmentsByPatientId(id);
        setAppointments(patientAppointments);
      } else {
        toast({
          variant: 'destructive',
          title: t('common.error'),
          description: t('patients.patientNotFound'),
        });
        navigate('/patients');
      }
    }
    setLoading(false);
  }, [id, navigate, toast, t]);
  
  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-medical"></div>
        </div>
      </DashboardLayout>
    );
  }
  
  if (!patient) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold">{t('patients.patientNotFound')}</h2>
            <p className="text-muted-foreground mt-2">{t('patients.patientNotFoundDescription')}</p>
            <Button asChild className="mt-4">
              <Link to="/patients">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('common.back')}
              </Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }
  
  // Count active treatments
  const activeTreatments = treatments.filter(treatment => 
    treatment.status === 'Active' || treatment.status === 'Scheduled'
  ).length;
  
  // Count upcoming appointments
  const upcomingAppointments = appointments.filter(appointment => 
    new Date(appointment.date) > new Date() && appointment.status === 'Scheduled'
  ).length;
  
  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild className="h-8 w-8">
              <Link to={`/patients/${patient.id}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">{t('treatments.treatmentTracking')}</h1>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}`}>
                <FileText className="mr-2 h-4 w-4" />
                {t('patients.patientDetails')}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}/exam-history`}>
                <Brain className="mr-2 h-4 w-4" />
                {t('scans.examHistory')}
              </Link>
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              {t('treatments.addTreatment')}
            </Button>
          </div>
        </div>
        
        {/* Patient Info Card */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-medical/10 flex items-center justify-center">
                <Activity className="h-5 w-5 text-medical" />
              </div>
              <div>
                <h2 className="font-semibold">
                  {patient.firstName} {patient.lastName}
                </h2>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline" className="bg-medical/10 text-medical border-medical/20">
                    ID: {patient.id}
                  </Badge>
                  <span>•</span>
                  <span>{t('treatments.activeTreatments')}: {activeTreatments}</span>
                  <span>•</span>
                  <span>{t('appointments.upcomingAppointments')}: {upcomingAppointments}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Treatment Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Clipboard className="h-4 w-4 text-medical" />
                {t('treatments.treatmentPlan')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeTreatments}</div>
              <p className="text-xs text-muted-foreground">
                {activeTreatments === 1 
                  ? t('treatments.activeTreatmentSingular') 
                  : t('treatments.activeTreatmentPlural')}
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4 text-medical" />
                {t('appointments.appointments')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{upcomingAppointments}</div>
              <p className="text-xs text-muted-foreground">
                {upcomingAppointments === 1 
                  ? t('appointments.upcomingAppointmentSingular') 
                  : t('appointments.upcomingAppointmentPlural')}
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Pill className="h-4 w-4 text-medical" />
                {t('medications.medications')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {treatments.filter(t => t.type === 'Medication' && t.status === 'Active').length}
              </div>
              <p className="text-xs text-muted-foreground">
                {t('medications.activeMedications')}
              </p>
            </CardContent>
          </Card>
        </div>
        
        {/* Tabs for different views */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="plan" className="flex items-center gap-2">
              <Clipboard className="h-4 w-4" />
              <span>{t('treatments.plan')}</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              <span>{t('treatments.history')}</span>
            </TabsTrigger>
            <TabsTrigger value="appointments" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>{t('appointments.appointments')}</span>
            </TabsTrigger>
            <TabsTrigger value="medications" className="flex items-center gap-2">
              <Pill className="h-4 w-4" />
              <span>{t('medications.medications')}</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="plan" className="mt-6">
            <TreatmentPlan treatments={treatments} />
          </TabsContent>
          
          <TabsContent value="history" className="mt-6">
            <TreatmentHistory treatments={treatments} />
          </TabsContent>
          
          <TabsContent value="appointments" className="mt-6">
            <AppointmentCalendar appointments={appointments} />
          </TabsContent>
          
          <TabsContent value="medications" className="mt-6">
            <MedicationTracking treatments={treatments} />
          </TabsContent>
        </Tabs>
        
        {/* Treatment Effectiveness Summary */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-medical" />
              {t('treatments.treatmentEffectiveness')}
            </CardTitle>
            <CardDescription>
              {t('treatments.treatmentEffectivenessDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">{t('treatments.overallResponse')}</div>
                <div className="font-medium">
                  {treatments.some(t => t.effectiveness === 'Excellent' || t.effectiveness === 'Good')
                    ? t('treatments.positive')
                    : treatments.some(t => t.effectiveness === 'Moderate')
                      ? t('treatments.moderate')
                      : t('treatments.insufficient')}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {t('treatments.basedOnCurrentTreatments')}
                </div>
              </div>
              
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">{t('treatments.sideEffects')}</div>
                <div className="font-medium">
                  {treatments.some(t => 
                    t.status === 'Active' && t.sideEffects.length > 2
                  )
                    ? t('treatments.significant')
                    : treatments.some(t => 
                        t.status === 'Active' && t.sideEffects.length > 0
                      )
                      ? t('treatments.mild')
                      : t('treatments.minimal')}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {t('treatments.monitoringRecommended')}
                </div>
              </div>
              
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">{t('treatments.nextSteps')}</div>
                <div className="font-medium">
                  {upcomingAppointments > 0
                    ? t('treatments.continueCurrentPlan')
                    : t('treatments.scheduleFollowUp')}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {t('treatments.recommendedByDoctor')}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default PatientTreatmentTracking;

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, 
  FileText, 
  Brain, 
  Calendar, 
  Upload, 
  BarChart, 
  SplitSquareVertical,
  Grid,
  Layers
} from 'lucide-react';
import { 
  getPatientById, 
  getScansByPatientId, 
  Patient, 
  Scan 
} from '@/data/mockPatients';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import ScanGallery from '@/components/ScanGallery';
import ScanComparison from '@/components/ScanComparison';
import TumorEvolution<PERSON>hart from '@/components/TumorEvolutionChart';

const PatientExamHistory: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [patient, setPatient] = useState<Patient | null>(null);
  const [scans, setScans] = useState<Scan[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('gallery');
  
  useEffect(() => {
    if (id) {
      const patientData = getPatientById(id);
      if (patientData) {
        setPatient(patientData);
        const patientScans = getScansByPatientId(id);
        setScans(patientScans);
      } else {
        toast({
          variant: 'destructive',
          title: t('common.error'),
          description: t('patients.patientNotFound'),
        });
        navigate('/patients');
      }
    }
    setLoading(false);
  }, [id, navigate, toast, t]);
  
  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-medical"></div>
        </div>
      </DashboardLayout>
    );
  }
  
  if (!patient) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold">{t('patients.patientNotFound')}</h2>
            <p className="text-muted-foreground mt-2">{t('patients.patientNotFoundDescription')}</p>
            <Button asChild className="mt-4">
              <Link to="/patients">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('common.back')}
              </Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }
  
  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild className="h-8 w-8">
              <Link to={`/patients/${patient.id}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">{t('scans.examHistory')}</h1>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}`}>
                <FileText className="mr-2 h-4 w-4" />
                {t('patients.patientDetails')}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}/treatment-tracking`}>
                <Calendar className="mr-2 h-4 w-4" />
                {t('treatments.treatmentTracking')}
              </Link>
            </Button>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              {t('scans.uploadNewScan')}
            </Button>
          </div>
        </div>
        
        {/* Patient Info Card */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-medical/10 flex items-center justify-center">
                <Brain className="h-5 w-5 text-medical" />
              </div>
              <div>
                <h2 className="font-semibold">
                  {patient.firstName} {patient.lastName}
                </h2>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline" className="bg-medical/10 text-medical border-medical/20">
                    ID: {patient.id}
                  </Badge>
                  <span>•</span>
                  <span>{t('scans.totalScans')}: {scans.length}</span>
                  <span>•</span>
                  <span>{t('scans.lastScan')}: {new Date(patient.lastScan).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabs for different views */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="gallery" className="flex items-center gap-2">
              <Grid className="h-4 w-4" />
              <span>{t('scans.gallery')}</span>
            </TabsTrigger>
            <TabsTrigger value="comparison" className="flex items-center gap-2">
              <SplitSquareVertical className="h-4 w-4" />
              <span>{t('scans.comparison')}</span>
            </TabsTrigger>
            <TabsTrigger value="evolution" className="flex items-center gap-2">
              <BarChart className="h-4 w-4" />
              <span>{t('scans.evolution')}</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="gallery" className="mt-6">
            <ScanGallery scans={scans} />
          </TabsContent>
          
          <TabsContent value="comparison" className="mt-6">
            <ScanComparison scans={scans} />
          </TabsContent>
          
          <TabsContent value="evolution" className="mt-6">
            <TumorEvolutionChart scans={scans} />
          </TabsContent>
        </Tabs>
        
        {/* AI Classification Results */}
        {scans.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-medical" />
                {t('scans.aiClassificationResults')}
              </CardTitle>
              <CardDescription>
                {t('scans.aiClassificationDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">{t('scans.latestDiagnosis')}</div>
                  <div className="font-medium">{scans[0].result.diagnosis}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {new Date(scans[0].date).toLocaleDateString()}
                  </div>
                </div>
                
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">{t('scans.tumorType')}</div>
                  <div className="font-medium">{scans[0].result.tumorType || t('common.unknown')}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {scans[0].result.malignant 
                      ? t('scans.malignant') 
                      : t('scans.benign')}
                  </div>
                </div>
                
                <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">{t('scans.recommendedAction')}</div>
                  <div className="font-medium">
                    {scans[0].result.malignant 
                      ? t('scans.continueTreatment') 
                      : t('scans.monitoringRecommended')}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {t('scans.basedOnLatestScan')}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default PatientExamHistory;

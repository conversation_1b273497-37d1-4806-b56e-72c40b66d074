"""
MLOps Manager - Interface unifiée pour la gestion MLOps
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from .model_registry import ModelRegistry
from .model_monitoring import ModelMonitor
from .model_deployment import ModelDeployment

logger = logging.getLogger(__name__)

class MLOpsManager:
    """
    Gestionnaire centralisé pour toutes les opérations MLOps
    """
    
    def __init__(self):
        self.registry = ModelRegistry()
        self.monitor = ModelMonitor()
        self.deployment = ModelDeployment(registry=self.registry, monitor=self.monitor)
        
        logger.info("MLOps Manager initialisé")
    
    # ==================== GESTION DES MODÈLES ====================
    
    def register_model(
        self,
        model_name: str,
        model_path: str,
        version: str,
        metrics: Dict[str, float],
        training_info: Dict[str, Any] = None,
        tags: List[str] = None
    ) -> str:
        """
        Enregistre un nouveau modèle avec ses métadonnées
        
        Args:
            model_name: Nom du modèle (ex: "unet_brain_segmentation")
            model_path: Chemin vers le fichier du modèle
            version: Version du modèle (ex: "1.0.0")
            metrics: Métriques de performance du modèle
            training_info: Informations sur l'entraînement
            tags: Tags pour la classification
            
        Returns:
            str: ID unique du modèle enregistré
        """
        try:
            metadata = {
                "metrics": metrics,
                "training_info": training_info or {},
                "model_config": {
                    "architecture": "U-Net",
                    "input_shape": [128, 128, 2],
                    "output_classes": 4,
                    "framework": "TensorFlow"
                },
                "dataset_info": {
                    "name": "BraTS 2020",
                    "size": "4.16 GB",
                    "modalities": ["FLAIR", "T1ce"]
                }
            }
            
            model_id = self.registry.register_model(
                model_name=model_name,
                model_path=model_path,
                version=version,
                metadata=metadata,
                tags=tags
            )
            
            logger.info(f"Modèle enregistré: {model_id}")
            return model_id
            
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du modèle: {e}")
            raise
    
    def deploy_to_staging(self, model_id: str) -> Dict:
        """Déploie un modèle en staging"""
        try:
            # Promotion du modèle au stage staging
            self.registry.promote_model(model_id, "staging")
            
            # Déploiement
            result = self.deployment.deploy_model(model_id, "staging")
            
            if result["success"]:
                logger.info(f"Modèle {model_id} déployé en staging")
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur lors du déploiement en staging: {e}")
            return {"success": False, "error": str(e)}
    
    def deploy_to_production(self, model_id: str, force: bool = False) -> Dict:
        """Déploie un modèle en production"""
        try:
            # Vérifications supplémentaires pour la production
            if not force:
                validation = self._validate_production_deployment(model_id)
                if not validation["valid"]:
                    return {
                        "success": False,
                        "error": "Validation production échouée",
                        "details": validation
                    }
            
            # Promotion du modèle au stage production
            self.registry.promote_model(model_id, "production")
            
            # Déploiement
            result = self.deployment.deploy_model(model_id, "production", force=force)
            
            if result["success"]:
                logger.info(f"Modèle {model_id} déployé en production")
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur lors du déploiement en production: {e}")
            return {"success": False, "error": str(e)}
    
    def _validate_production_deployment(self, model_id: str) -> Dict:
        """Validations spécifiques pour le déploiement en production"""
        validation = {"valid": True, "checks": {}}
        
        # Vérification que le modèle a été testé en staging
        model = self.registry.get_model(model_id)
        if not model:
            validation["valid"] = False
            validation["checks"]["model_exists"] = False
            return validation
        
        # Vérification du stage staging
        has_staging = any(
            h.get("stage") == "staging" 
            for h in model.get("promotion_history", [])
        )
        validation["checks"]["tested_in_staging"] = has_staging
        
        if not has_staging:
            validation["valid"] = False
        
        # Vérification des métriques minimales
        metrics = model.get("metrics", {})
        min_accuracy = 0.85  # 85% minimum pour la production
        
        accuracy = metrics.get("accuracy", 0)
        validation["checks"]["accuracy_threshold"] = {
            "valid": accuracy >= min_accuracy,
            "current": accuracy,
            "minimum": min_accuracy
        }
        
        if accuracy < min_accuracy:
            validation["valid"] = False
        
        return validation
    
    # ==================== MONITORING ====================
    
    def log_prediction(
        self,
        model_id: str,
        input_metadata: Dict,
        prediction_result: Dict,
        inference_time_ms: float
    ):
        """Enregistre une prédiction pour le monitoring"""
        try:
            self.monitor.log_prediction(
                model_id=model_id,
                input_data=input_metadata,
                prediction=prediction_result,
                inference_time_ms=inference_time_ms,
                confidence=prediction_result.get("confidence")
            )
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement de la prédiction: {e}")
    
    def log_error(self, model_id: str, error_type: str, error_message: str):
        """Enregistre une erreur de prédiction"""
        try:
            self.monitor.log_error(model_id, error_type, error_message)
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement de l'erreur: {e}")
    
    def get_model_health(self, model_id: str = None) -> Dict:
        """Récupère l'état de santé des modèles"""
        try:
            # Métriques de monitoring
            monitoring_summary = self.monitor.get_metrics_summary(hours=24)
            
            # Statut des déploiements
            deployment_status = self.deployment.get_deployment_status()
            
            # Alertes actives
            active_alerts = self.monitor.get_active_alerts()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "monitoring": monitoring_summary,
                "deployments": deployment_status,
                "alerts": {
                    "count": len(active_alerts),
                    "active": active_alerts
                },
                "overall_status": self._calculate_overall_health(
                    monitoring_summary, deployment_status, active_alerts
                )
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de l'état de santé: {e}")
            return {"error": str(e)}
    
    def _calculate_overall_health(self, monitoring: Dict, deployments: Dict, alerts: List) -> str:
        """Calcule l'état de santé global"""
        try:
            # Vérification des alertes critiques
            critical_alerts = [
                a for a in alerts 
                if a.get("severity") == "HIGH" and not a.get("resolved", False)
            ]
            
            if critical_alerts:
                return "CRITICAL"
            
            # Vérification du taux d'erreur
            error_rate = monitoring.get("error_rate_percent", 0)
            if error_rate > 10:  # Plus de 10% d'erreurs
                return "UNHEALTHY"
            
            # Vérification des déploiements
            unhealthy_deployments = [
                env for env, status in deployments.items()
                if status.get("health_status", {}).get("status") != "healthy"
            ]
            
            if unhealthy_deployments:
                return "DEGRADED"
            
            # Vérification des performances
            avg_inference_time = monitoring.get("inference_time", {}).get("avg_ms", 0)
            if avg_inference_time > 3000:  # Plus de 3 secondes
                return "SLOW"
            
            return "HEALTHY"
            
        except Exception as e:
            logger.error(f"Erreur lors du calcul de l'état de santé: {e}")
            return "UNKNOWN"
    
    # ==================== OPÉRATIONS AVANCÉES ====================
    
    def rollback_production(self) -> Dict:
        """Effectue un rollback en production"""
        try:
            result = self.deployment.rollback_deployment("production")
            
            if result["success"]:
                logger.warning("Rollback en production effectué")
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur lors du rollback: {e}")
            return {"success": False, "error": str(e)}
    
    def get_dashboard_data(self) -> Dict:
        """Récupère toutes les données pour le dashboard MLOps"""
        try:
            return {
                "timestamp": datetime.now().isoformat(),
                "models": {
                    "total": len(self.registry.list_models()),
                    "by_stage": self._get_models_by_stage(),
                    "registry_stats": self.registry.get_registry_stats()
                },
                "deployments": {
                    "status": self.deployment.get_deployment_status(),
                    "recent": self.deployment.list_deployments(limit=5)
                },
                "monitoring": {
                    "summary": self.monitor.get_metrics_summary(hours=24),
                    "alerts": {
                        "active": self.monitor.get_active_alerts(),
                        "count_by_severity": self._count_alerts_by_severity()
                    }
                },
                "health": self.get_model_health()
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des données dashboard: {e}")
            return {"error": str(e)}
    
    def _get_models_by_stage(self) -> Dict:
        """Compte les modèles par stage"""
        models = self.registry.list_models()
        stages = {}
        
        for model in models:
            stage = model.get("stage", "none")
            stages[stage] = stages.get(stage, 0) + 1
        
        return stages
    
    def _count_alerts_by_severity(self) -> Dict:
        """Compte les alertes par sévérité"""
        alerts = self.monitor.get_active_alerts()
        severity_count = {}
        
        for alert in alerts:
            severity = alert.get("severity", "UNKNOWN")
            severity_count[severity] = severity_count.get(severity, 0) + 1
        
        return severity_count
    
    # ==================== UTILITAIRES ====================
    
    def cleanup_old_models(self, keep_versions: int = 5) -> Dict:
        """Nettoie les anciennes versions des modèles"""
        try:
            models_by_name = {}
            all_models = self.registry.list_models()
            
            # Groupement par nom de modèle
            for model in all_models:
                name = model["model_name"]
                if name not in models_by_name:
                    models_by_name[name] = []
                models_by_name[name].append(model)
            
            archived_count = 0
            
            # Archivage des anciennes versions
            for name, models in models_by_name.items():
                # Tri par date d'enregistrement (plus récent en premier)
                models.sort(key=lambda x: x["registered_at"], reverse=True)
                
                # Archivage des versions anciennes (garde les N plus récentes)
                for model in models[keep_versions:]:
                    if model.get("stage") not in ["production", "staging"]:
                        self.registry.archive_model(model["model_id"])
                        archived_count += 1
            
            logger.info(f"Nettoyage terminé: {archived_count} modèles archivés")
            
            return {
                "success": True,
                "archived_count": archived_count,
                "models_by_name": {name: len(models) for name, models in models_by_name.items()}
            }
            
        except Exception as e:
            logger.error(f"Erreur lors du nettoyage: {e}")
            return {"success": False, "error": str(e)}
    
    def export_mlops_report(self, days: int = 7) -> Dict:
        """Exporte un rapport MLOps complet"""
        try:
            from datetime import timedelta
            
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            end_date = datetime.now().isoformat()
            
            return {
                "report_period": {"start": start_date, "end": end_date, "days": days},
                "generated_at": datetime.now().isoformat(),
                "models": {
                    "registry_stats": self.registry.get_registry_stats(),
                    "all_models": self.registry.list_models()
                },
                "deployments": {
                    "history": self.deployment.list_deployments(limit=50),
                    "current_status": self.deployment.get_deployment_status()
                },
                "monitoring": {
                    "metrics": self.monitor.export_metrics(start_date, end_date),
                    "summary": self.monitor.get_metrics_summary(hours=days*24)
                },
                "health": self.get_model_health()
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de l'export du rapport: {e}")
            return {"error": str(e)}

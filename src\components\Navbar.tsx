import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Brain, Menu, X } from 'lucide-react';
import ThemeSwitcher from '@/components/ThemeSwitcher';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const Navbar = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when location changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white/80 dark:bg-slate-900/80 backdrop-blur-md shadow-sm py-3' : 'bg-transparent py-5'
      }`}
    >
      <div className="container-custom">
        <div className="flex items-center justify-between">
          <Link
            to="/"
            className="flex items-center space-x-2 transition-transform duration-300 hover:scale-[1.02]"
          >
            <Brain className="h-8 w-8 text-medical" strokeWidth={1.5} />
            <span className="text-xl font-semibold text-slate-800 dark:text-white">{t('common.appName')}</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            <NavLink to="/" label={t('navigation.home')} />
            <NavLink to="/about" label={t('navigation.about')} />
            <NavLink to="/dashboard" label={t('navigation.dashboard')} />
            <NavLink to="/patients" label={t('navigation.patients')} />
            <NavLink to="/users" label={t('navigation.users')} />
            <NavLink to="/settings" label={t('navigation.settings')} />
            
            <div className="flex items-center space-x-2 ml-4">
              <ThemeSwitcher />
              <LanguageSwitcher />
            </div>
          </nav>

          {/* Mobile Menu Button */}
          <div className="flex items-center md:hidden">
            <ThemeSwitcher />
            <LanguageSwitcher />
            <button
              className="p-2 text-slate-700 dark:text-slate-200 focus:outline-none"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white dark:bg-slate-800 shadow-lg animate-slide-down">
          <div className="container-custom py-4 flex flex-col space-y-4">
            <NavLink to="/" label={t('navigation.home')} mobile />
            <NavLink to="/about" label={t('navigation.about')} mobile />
            <NavLink to="/dashboard" label={t('navigation.dashboard')} mobile />
            <NavLink to="/patients" label={t('navigation.patients')} mobile />
            <NavLink to="/users" label={t('navigation.users')} mobile />
            <NavLink to="/settings" label={t('navigation.settings')} mobile />
          </div>
        </div>
      )}
    </header>
  );
};

interface NavLinkProps {
  to: string;
  label: string;
  mobile?: boolean;
}

const NavLink = ({ to, label, mobile = false }: NavLinkProps) => {
  const location = useLocation();
  const isActive = location.pathname === to;

  const baseClasses = "transition-all duration-300";
  const mobileClasses = "block py-2 px-4 rounded-md text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700";
  const desktopClasses = "font-medium hover:text-medical";
  const activeClasses = "text-medical";

  return (
    <Link
      to={to}
      className={`${baseClasses} ${mobile ? mobileClasses : desktopClasses} ${
        isActive ? activeClasses : ""
      }`}
    >
      {label}
    </Link>
  );
};

export default Navbar;

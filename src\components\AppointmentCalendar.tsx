import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format, isSameDay, isToday, isFuture, isPast, addMonths } from 'date-fns';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  User, 
  FileText, 
  Plus,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Appointment } from '@/data/mockPatients';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface AppointmentCalendarProps {
  appointments: Appointment[];
}

const AppointmentCalendar: React.FC<AppointmentCalendarProps> = ({ appointments }) => {
  const { t } = useTranslation();
  const [date, setDate] = useState<Date>(new Date());
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  
  // Filter upcoming appointments
  const upcomingAppointments = appointments.filter(appointment => 
    isFuture(new Date(appointment.date)) && appointment.status === 'Scheduled'
  ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  // Get appointments for the selected date
  const appointmentsForDate = appointments.filter(appointment => 
    isSameDay(new Date(appointment.date), date)
  ).sort((a, b) => {
    // Sort by time
    const timeA = a.time.split(' ')[0].split(':').map(Number);
    const timeB = b.time.split(' ')[0].split(':').map(Number);
    const isPMA = a.time.includes('PM');
    const isPMB = b.time.includes('PM');
    
    // Convert to 24-hour format for comparison
    let hoursA = timeA[0];
    let hoursB = timeB[0];
    
    if (isPMA && hoursA < 12) hoursA += 12;
    if (isPMB && hoursB < 12) hoursB += 12;
    
    if (hoursA !== hoursB) return hoursA - hoursB;
    return timeA[1] - timeB[1];
  });
  
  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'default';
      case 'Scheduled':
        return 'outline';
      case 'Cancelled':
        return 'destructive';
      case 'No-Show':
        return 'secondary';
      default:
        return 'outline';
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'Scheduled':
        return <CalendarIcon className="h-4 w-4 text-blue-500" />;
      case 'Cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'No-Show':
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
      default:
        return <CalendarIcon className="h-4 w-4" />;
    }
  };
  
  // Function to highlight dates with appointments
  const isDayWithAppointment = (day: Date) => {
    return appointments.some(appointment => 
      isSameDay(new Date(appointment.date), day)
    );
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('appointments.appointmentCalendar')}</CardTitle>
        <CardDescription>{t('appointments.appointmentCalendarDescription')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Calendar */}
          <div>
            <Calendar
              mode="single"
              selected={date}
              onSelect={(newDate) => newDate && setDate(newDate)}
              className="rounded-md border"
              modifiers={{
                appointment: (date) => isDayWithAppointment(date),
                today: (date) => isToday(date)
              }}
              modifiersClassNames={{
                appointment: 'bg-medical/10 font-medium text-medical',
                today: 'bg-medical/20 font-bold text-medical'
              }}
            />
          </div>
          
          {/* Appointments for selected date */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">
                {format(date, 'PPP')}
              </h3>
              <Button variant="outline" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                {t('appointments.addAppointment')}
              </Button>
            </div>
            
            {appointmentsForDate.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <CalendarIcon className="h-10 w-10 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">{t('appointments.noAppointmentsForDate')}</p>
              </div>
            ) : (
              <div className="space-y-3">
                {appointmentsForDate.map(appointment => (
                  <Dialog key={appointment.id}>
                    <DialogTrigger asChild>
                      <div 
                        className="flex items-start justify-between p-3 rounded-md border cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
                        onClick={() => setSelectedAppointment(appointment)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="mt-0.5">
                            {getStatusIcon(appointment.status)}
                          </div>
                          <div>
                            <div className="font-medium">{appointment.purpose}</div>
                            <div className="text-xs text-muted-foreground">
                              {appointment.time} • {appointment.doctor}
                            </div>
                          </div>
                        </div>
                        <Badge variant={getStatusBadgeVariant(appointment.status)}>
                          {appointment.status}
                        </Badge>
                      </div>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>{appointment.purpose}</DialogTitle>
                        <DialogDescription>
                          {format(new Date(appointment.date), 'PPP')} • {appointment.time}
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-2">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm">
                              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('appointments.date')}:</span>
                              <span>{format(new Date(appointment.date), 'PPP')}</span>
                            </div>
                            
                            <div className="flex items-center gap-2 text-sm">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('appointments.time')}:</span>
                              <span>{appointment.time}</span>
                            </div>
                            
                            <div className="flex items-center gap-2 text-sm">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('appointments.doctor')}:</span>
                              <span>{appointment.doctor}</span>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('appointments.purpose')}:</span>
                              <span>{appointment.purpose}</span>
                            </div>
                            
                            <div className="flex items-center gap-2 text-sm">
                              {getStatusIcon(appointment.status)}
                              <span className="text-muted-foreground">{t('appointments.status')}:</span>
                              <Badge variant={getStatusBadgeVariant(appointment.status)}>
                                {appointment.status}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-2 text-sm">
                              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('appointments.followUp')}:</span>
                              <span>{appointment.followUp ? t('common.yes') : t('common.no')}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="pt-2 border-t">
                          <div className="text-sm text-muted-foreground mb-1">{t('appointments.notes')}:</div>
                          <p className="text-sm">{appointment.notes}</p>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        {appointment.status === 'Scheduled' && (
                          <>
                            <Button variant="outline" className="text-red-500">
                              <XCircle className="mr-2 h-4 w-4" />
                              {t('appointments.cancel')}
                            </Button>
                            <Button>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              {t('appointments.confirm')}
                            </Button>
                          </>
                        )}
                        {appointment.status === 'Completed' && (
                          <Button className="ml-auto">
                            <FileText className="mr-2 h-4 w-4" />
                            {t('appointments.viewSummary')}
                          </Button>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Upcoming appointments */}
        <div className="pt-4 border-t">
          <h3 className="font-medium mb-3">{t('appointments.upcomingAppointments')}</h3>
          
          {upcomingAppointments.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-muted-foreground">{t('appointments.noUpcomingAppointments')}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {upcomingAppointments.slice(0, 3).map(appointment => (
                <div key={appointment.id} className="flex items-start justify-between p-3 rounded-md bg-slate-50 dark:bg-slate-800">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <CalendarIcon className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <div className="font-medium">{appointment.purpose}</div>
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(appointment.date), 'PPP')} • {appointment.time} • {appointment.doctor}
                      </div>
                    </div>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('appointments.viewDetails')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              ))}
              
              {upcomingAppointments.length > 3 && (
                <Button variant="outline" className="w-full">
                  {t('appointments.viewAllAppointments')} ({upcomingAppointments.length})
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">
          <ChevronLeft className="mr-2 h-4 w-4" />
          {format(addMonths(date, -1), 'MMMM yyyy')}
        </Button>
        <Button variant="outline">
          {format(addMonths(date, 1), 'MMMM yyyy')}
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AppointmentCalendar;

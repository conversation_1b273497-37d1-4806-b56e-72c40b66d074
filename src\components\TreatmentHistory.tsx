import React from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Calendar, 
  Clock, 
  Pill, 
  Stethoscope, 
  FileText, 
  Scissors, 
  Radiation,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  ChevronRight
} from 'lucide-react';
import { Treatment } from '@/data/mockPatients';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface TreatmentHistoryProps {
  treatments: Treatment[];
}

const TreatmentHistory: React.FC<TreatmentHistoryProps> = ({ treatments }) => {
  const { t } = useTranslation();
  
  // Filter completed treatments
  const completedTreatments = treatments.filter(treatment => 
    treatment.status === 'Completed'
  );
  
  // Filter cancelled treatments
  const cancelledTreatments = treatments.filter(treatment => 
    treatment.status === 'Cancelled'
  );
  
  // Sort treatments by date (newest first)
  const sortedCompletedTreatments = [...completedTreatments].sort((a, b) => 
    new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  );
  
  const sortedCancelledTreatments = [...cancelledTreatments].sort((a, b) => 
    new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  );
  
  // Get icon based on treatment type
  const getTreatmentIcon = (type: string) => {
    switch (type) {
      case 'Medication':
        return <Pill className="h-5 w-5 text-blue-500" />;
      case 'Surgery':
        return <Scissors className="h-5 w-5 text-red-500" />;
      case 'Radiation':
        return <Radiation className="h-5 w-5 text-amber-500" />;
      case 'Chemotherapy':
        return <Activity className="h-5 w-5 text-purple-500" />;
      default:
        return <FileText className="h-5 w-5 text-slate-500" />;
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'Cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
    }
  };
  
  if (completedTreatments.length === 0 && cancelledTreatments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('treatments.treatmentHistory')}</CardTitle>
          <CardDescription>{t('treatments.treatmentHistoryDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <FileText className="h-10 w-10 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">{t('treatments.noTreatmentHistory')}</p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('treatments.treatmentHistory')}</CardTitle>
        <CardDescription>{t('treatments.treatmentHistoryDescription')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Completed treatments */}
        {sortedCompletedTreatments.length > 0 && (
          <div>
            <h4 className="font-medium flex items-center gap-2 mb-3">
              <CheckCircle className="h-4 w-4 text-green-500" />
              {t('treatments.completedTreatments')}
            </h4>
            <Accordion type="single" collapsible className="w-full">
              {sortedCompletedTreatments.map((treatment, index) => (
                <AccordionItem key={treatment.id} value={treatment.id}>
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center gap-3 text-left">
                      <div>
                        {getTreatmentIcon(treatment.type)}
                      </div>
                      <div>
                        <div className="font-medium">{treatment.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {treatment.type} • {format(new Date(treatment.startDate), 'PP')}
                          {treatment.endDate && ` - ${format(new Date(treatment.endDate), 'PP')}`}
                        </div>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="pt-2 pb-1 px-1">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{t('treatments.startDate')}:</span>
                            <span>{format(new Date(treatment.startDate), 'PPP')}</span>
                          </div>
                          
                          {treatment.endDate && (
                            <div className="flex items-center gap-2 text-sm">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('treatments.endDate')}:</span>
                              <span>{format(new Date(treatment.endDate), 'PPP')}</span>
                            </div>
                          )}
                          
                          {treatment.frequency && (
                            <div className="flex items-center gap-2 text-sm">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('treatments.frequency')}:</span>
                              <span>{treatment.frequency}</span>
                            </div>
                          )}
                          
                          {treatment.dosage && (
                            <div className="flex items-center gap-2 text-sm">
                              <Pill className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('treatments.dosage')}:</span>
                              <span>{treatment.dosage}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <Stethoscope className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{t('treatments.prescribedBy')}:</span>
                            <span>{treatment.doctor}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{t('treatments.effectiveness')}:</span>
                            <span>{treatment.effectiveness}</span>
                          </div>
                          
                          {treatment.sideEffects.length > 0 && (
                            <div className="flex items-start gap-2 text-sm">
                              <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5" />
                              <div>
                                <span className="text-muted-foreground">{t('treatments.sideEffects')}:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {treatment.sideEffects.map((effect, index) => (
                                    <Badge key={index} variant="outline" className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800">
                                      {effect}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="pt-3 mt-2 border-t">
                        <div className="text-sm text-muted-foreground mb-1">{t('treatments.notes')}:</div>
                        <p className="text-sm">{treatment.notes}</p>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        )}
        
        {/* Cancelled treatments */}
        {sortedCancelledTreatments.length > 0 && (
          <div>
            <h4 className="font-medium flex items-center gap-2 mb-3">
              <XCircle className="h-4 w-4 text-red-500" />
              {t('treatments.cancelledTreatments')}
            </h4>
            <Accordion type="single" collapsible className="w-full">
              {sortedCancelledTreatments.map((treatment, index) => (
                <AccordionItem key={treatment.id} value={treatment.id}>
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center gap-3 text-left">
                      <div>
                        {getTreatmentIcon(treatment.type)}
                      </div>
                      <div>
                        <div className="font-medium">{treatment.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {treatment.type} • {format(new Date(treatment.startDate), 'PP')}
                          {treatment.endDate && ` - ${format(new Date(treatment.endDate), 'PP')}`}
                        </div>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="pt-2 pb-1 px-1">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{t('treatments.startDate')}:</span>
                            <span>{format(new Date(treatment.startDate), 'PPP')}</span>
                          </div>
                          
                          {treatment.endDate && (
                            <div className="flex items-center gap-2 text-sm">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('treatments.endDate')}:</span>
                              <span>{format(new Date(treatment.endDate), 'PPP')}</span>
                            </div>
                          )}
                          
                          {treatment.frequency && (
                            <div className="flex items-center gap-2 text-sm">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">{t('treatments.frequency')}:</span>
                              <span>{treatment.frequency}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <Stethoscope className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{t('treatments.prescribedBy')}:</span>
                            <span>{treatment.doctor}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <XCircle className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{t('treatments.cancellationReason')}:</span>
                            <span>{t('treatments.notSpecified')}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="pt-3 mt-2 border-t">
                        <div className="text-sm text-muted-foreground mb-1">{t('treatments.notes')}:</div>
                        <p className="text-sm">{treatment.notes}</p>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="ml-auto">
          <FileText className="mr-2 h-4 w-4" />
          {t('treatments.exportTreatmentHistory')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TreatmentHistory;

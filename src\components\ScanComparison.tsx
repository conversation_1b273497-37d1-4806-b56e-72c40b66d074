import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  ArrowLeft, 
  ArrowRight, 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  Minimize,
  Calendar,
  FileText,
  Pencil,
  Download
} from 'lucide-react';
import { Scan } from '@/data/mockPatients';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';

interface ScanComparisonProps {
  scans: Scan[];
}

const ScanComparison: React.FC<ScanComparisonProps> = ({ scans }) => {
  const { t } = useTranslation();
  const [leftScanId, setLeftScanId] = useState<string | null>(scans.length > 0 ? scans[1]?.id || null : null);
  const [rightScanId, setRightScanId] = useState<string | null>(scans.length > 0 ? scans[0]?.id || null : null);
  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  
  // Sort scans by date (newest first)
  const sortedScans = [...scans].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  const leftScan = leftScanId ? scans.find(scan => scan.id === leftScanId) : null;
  const rightScan = rightScanId ? scans.find(scan => scan.id === rightScanId) : null;

  const handleZoomIn = () => {
    setZoomLevel(Math.min(zoomLevel + 10, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(Math.max(zoomLevel - 10, 50));
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (scans.length < 2) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('scans.scanComparison')}</CardTitle>
          <CardDescription>{t('scans.scanComparisonDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <FileText className="h-10 w-10 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">{t('scans.notEnoughScans')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={isFullscreen ? 'fixed inset-0 z-50 rounded-none' : ''}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{t('scans.scanComparison')}</CardTitle>
            <CardDescription>{t('scans.scanComparisonDescription')}</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={handleZoomOut} disabled={zoomLevel <= 50}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleZoomIn} disabled={zoomLevel >= 200}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={toggleFullscreen}>
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Left scan selector */}
          <div>
            <div className="mb-2">
              <Select
                value={leftScanId || ''}
                onValueChange={(value) => setLeftScanId(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('scans.selectScan')} />
                </SelectTrigger>
                <SelectContent>
                  {sortedScans.map((scan) => (
                    <SelectItem key={scan.id} value={scan.id}>
                      {format(new Date(scan.date), 'PPP')} - {scan.type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {leftScan && (
              <div className="space-y-3">
                <div className="relative overflow-hidden rounded-md border">
                  <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top left' }}>
                    <img
                      src={leftScan.imageUrl}
                      alt={`${leftScan.type} - ${leftScan.bodyPart}`}
                      className="w-full transition-transform"
                    />
                  </div>
                  <div className="absolute top-2 left-2">
                    <Badge variant="secondary">
                      {format(new Date(leftScan.date), 'PPP')}
                    </Badge>
                  </div>
                </div>
                <div className="text-sm space-y-1">
                  <div className="font-medium">{leftScan.result.diagnosis}</div>
                  <div className="text-muted-foreground">
                    {leftScan.result.tumorSize && (
                      <span className="mr-3">{t('scans.size')}: {leftScan.result.tumorSize}</span>
                    )}
                    {leftScan.result.tumorType && (
                      <span>{t('scans.type')}: {leftScan.result.tumorType}</span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right scan selector */}
          <div>
            <div className="mb-2">
              <Select
                value={rightScanId || ''}
                onValueChange={(value) => setRightScanId(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('scans.selectScan')} />
                </SelectTrigger>
                <SelectContent>
                  {sortedScans.map((scan) => (
                    <SelectItem key={scan.id} value={scan.id}>
                      {format(new Date(scan.date), 'PPP')} - {scan.type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {rightScan && (
              <div className="space-y-3">
                <div className="relative overflow-hidden rounded-md border">
                  <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top left' }}>
                    <img
                      src={rightScan.imageUrl}
                      alt={`${rightScan.type} - ${rightScan.bodyPart}`}
                      className="w-full transition-transform"
                    />
                  </div>
                  <div className="absolute top-2 left-2">
                    <Badge variant="secondary">
                      {format(new Date(rightScan.date), 'PPP')}
                    </Badge>
                  </div>
                </div>
                <div className="text-sm space-y-1">
                  <div className="font-medium">{rightScan.result.diagnosis}</div>
                  <div className="text-muted-foreground">
                    {rightScan.result.tumorSize && (
                      <span className="mr-3">{t('scans.size')}: {rightScan.result.tumorSize}</span>
                    )}
                    {rightScan.result.tumorType && (
                      <span>{t('scans.type')}: {rightScan.result.tumorType}</span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {leftScan && rightScan && (
          <div className="mt-6 pt-4 border-t">
            <h4 className="font-medium mb-2">{t('scans.comparisonAnalysis')}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm">
                  <span className="text-muted-foreground">{t('scans.timeBetweenScans')}: </span>
                  <span className="font-medium">
                    {Math.abs(
                      Math.round(
                        (new Date(leftScan.date).getTime() - new Date(rightScan.date).getTime()) / 
                        (1000 * 60 * 60 * 24)
                      )
                    )} {t('common.days')}
                  </span>
                </div>
                {leftScan.result.tumorSize && rightScan.result.tumorSize && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">{t('scans.sizeChange')}: </span>
                    <span className="font-medium">
                      {parseFloat(leftScan.result.tumorSize) > parseFloat(rightScan.result.tumorSize) 
                        ? t('scans.decreased') 
                        : parseFloat(leftScan.result.tumorSize) < parseFloat(rightScan.result.tumorSize)
                          ? t('scans.increased')
                          : t('scans.unchanged')}
                    </span>
                  </div>
                )}
              </div>
              <div className="text-sm">
                <div className="text-muted-foreground mb-1">{t('scans.notes')}:</div>
                <p>
                  {leftScan.date > rightScan.date 
                    ? leftScan.result.notes 
                    : rightScan.result.notes}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          {t('scans.zoomLevel')}: {zoomLevel}%
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('common.export')}
          </Button>
          <Button variant="outline" size="sm">
            <Pencil className="mr-2 h-4 w-4" />
            {t('common.annotate')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ScanComparison;

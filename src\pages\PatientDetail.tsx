import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Activity,
  Heart,
  UserCircle,
  FileText,
  Pencil,
  Trash2,
  Clock,
  Building,
  Shield,
  Ruler,
  Weight
} from 'lucide-react';
import {
  getPatientById,
  getScansByPatientId,
  getTreatmentsByPatientId,
  getAppointmentsByPatientId,
  Patient
} from '@/data/mockPatients';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import PatientTimeline from '@/components/PatientTimeline';
import PatientMedicalInfo from '@/components/PatientMedicalInfo';

// Helper function to calculate age
const calculateAge = (dateOfBirth: string): number => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

// Helper function to get initials from name
const getInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
};

const PatientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Determine which view to show based on the URL
  const isEditMode = location.pathname.includes('/edit');
  const isScansMode = location.pathname.includes('/scans');

  useEffect(() => {
    if (id) {
      const patientData = getPatientById(id);
      if (patientData) {
        setPatient(patientData);
      } else {
        toast({
          variant: 'destructive',
          title: t('common.error'),
          description: t('patients.patientNotFound'),
        });
        navigate('/patients');
      }
    }
    setLoading(false);
  }, [id, navigate, toast, t]);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-medical"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!patient) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold">{t('patients.patientNotFound')}</h2>
            <p className="text-muted-foreground mt-2">{t('patients.patientNotFoundDescription')}</p>
            <Button asChild className="mt-4">
              <Link to="/patients">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('common.back')}
              </Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const age = calculateAge(patient.dateOfBirth);
  const scans = getScansByPatientId(patient.id);
  const treatments = getTreatmentsByPatientId(patient.id);
  const appointments = getAppointmentsByPatientId(patient.id);

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild className="h-8 w-8">
              <Link to="/patients">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">{t('patients.patientDetails')}</h1>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}/edit`}>
                <Pencil className="mr-2 h-4 w-4" />
                {t('common.edit')}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}/exam-history`}>
                <FileText className="mr-2 h-4 w-4" />
                {t('scans.examHistory')}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to={`/patients/${patient.id}/treatment-tracking`}>
                <Activity className="mr-2 h-4 w-4" />
                {t('treatments.treatmentTracking')}
              </Link>
            </Button>
          </div>
        </div>

        {/* Patient Profile Card */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Avatar and basic info */}
              <div className="flex flex-col items-center md:items-start gap-4">
                <Avatar className="h-24 w-24 border-2 border-slate-200 dark:border-slate-700">
                  <AvatarFallback className="text-2xl bg-medical/10 text-medical">
                    {getInitials(patient.firstName, patient.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-center md:text-left">
                  <h2 className="text-2xl font-bold">
                    {patient.firstName} {patient.lastName}
                  </h2>
                  <div className="flex items-center gap-2 mt-1 text-muted-foreground">
                    <Badge variant="outline" className="bg-medical/10 text-medical border-medical/20">
                      ID: {patient.id}
                    </Badge>
                    <span>•</span>
                    <span>{age} {t('common.yearsOld')}</span>
                    <span>•</span>
                    <span>{patient.gender}</span>
                  </div>
                </div>
              </div>

              {/* Contact information */}
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 md:mt-0">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{patient.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{patient.contactNumber}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{patient.address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {t('patients.dateOfBirth')}: {new Date(patient.dateOfBirth).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {t('patients.bloodType')}: {patient.bloodType}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Ruler className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {t('patients.height')}: {patient.height} cm
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Weight className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {t('patients.weight')}: {patient.weight} kg
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <UserCircle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {t('patients.assignedDoctor')}: {patient.doctor}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Key stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
              <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                <div className="text-xs text-muted-foreground mb-1">{t('patients.lastVisit')}</div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-medical" />
                  <span className="font-medium">{new Date(patient.lastVisit).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                <div className="text-xs text-muted-foreground mb-1">{t('patients.lastScan')}</div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-medical" />
                  <span className="font-medium">{new Date(patient.lastScan).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                <div className="text-xs text-muted-foreground mb-1">{t('patients.nextAppointment')}</div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-medical" />
                  <span className="font-medium">
                    {patient.nextAppointment
                      ? new Date(patient.nextAppointment).toLocaleDateString()
                      : t('patients.noUpcomingAppointments')}
                  </span>
                </div>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                <div className="text-xs text-muted-foreground mb-1">{t('patients.insurance')}</div>
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-medical" />
                  <span className="font-medium">{patient.insurance.provider}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contact */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Phone className="h-5 w-5 text-red-500" />
              {t('patients.emergencyContact')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">{t('patients.name')}</div>
                <div className="font-medium">{patient.emergencyContact.name}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">{t('patients.relationship')}</div>
                <div className="font-medium">{patient.emergencyContact.relationship}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">{t('patients.contactNumber')}</div>
                <div className="font-medium">{patient.emergencyContact.phone}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Insurance Information */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-500" />
              {t('patients.insuranceInformation')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">{t('patients.provider')}</div>
                <div className="font-medium">{patient.insurance.provider}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">{t('patients.policyNumber')}</div>
                <div className="font-medium">{patient.insurance.policyNumber}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">{t('patients.expiryDate')}</div>
                <div className="font-medium">{new Date(patient.insurance.expiryDate).toLocaleDateString()}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Conditional content based on mode */}
        {isEditMode ? (
          <Card>
            <CardHeader>
              <CardTitle>{t('patients.editPatient')}</CardTitle>
              <CardDescription>{t('patients.editPatientDescription')}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">{t('common.comingSoon')}</p>
              {/* Patient edit form would go here */}
            </CardContent>
          </Card>
        ) : isScansMode ? (
          <Card>
            <CardHeader>
              <CardTitle>{t('navigation.scans')}</CardTitle>
              <CardDescription>{t('scans.patientScansDescription')}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">{t('common.comingSoon')}</p>
              {/* Patient scans would go here */}
            </CardContent>
          </Card>
        ) : (
          /* Default view - Tabs for Medical Info and Timeline */
          <Tabs defaultValue="medical-info" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="medical-info">{t('patients.medicalInformation')}</TabsTrigger>
              <TabsTrigger value="timeline">{t('patients.timeline')}</TabsTrigger>
            </TabsList>
            <TabsContent value="medical-info" className="mt-6">
              <PatientMedicalInfo patient={patient} />
            </TabsContent>
            <TabsContent value="timeline" className="mt-6">
              <PatientTimeline
                appointments={appointments}
                scans={scans}
                treatments={treatments}
              />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </DashboardLayout>
  );
};

export default PatientDetail;

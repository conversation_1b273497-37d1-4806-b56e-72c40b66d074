import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Calendar, 
  FileText, 
  ZoomIn, 
  Download, 
  ChevronLeft, 
  ChevronRight,
  Pencil,
  Trash2
} from 'lucide-react';
import { Scan } from '@/data/mockPatients';
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem, 
  CarouselNext, 
  CarouselPrevious 
} from '@/components/ui/carousel';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface ScanGalleryProps {
  scans: Scan[];
  onSelectScan?: (scan: Scan) => void;
}

const ScanGallery: React.FC<ScanGalleryProps> = ({ scans, onSelectScan }) => {
  const { t } = useTranslation();
  const [selectedScan, setSelectedScan] = useState<Scan | null>(null);
  
  // Sort scans by date (newest first)
  const sortedScans = [...scans].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  const handleScanClick = (scan: Scan) => {
    setSelectedScan(scan);
    if (onSelectScan) {
      onSelectScan(scan);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('scans.scanGallery')}</h3>
        <span className="text-sm text-muted-foreground">
          {sortedScans.length} {t('scans.scansAvailable')}
        </span>
      </div>

      {sortedScans.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <FileText className="h-10 w-10 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{t('scans.noScans')}</p>
          </CardContent>
        </Card>
      ) : (
        <Carousel
          opts={{
            align: "start",
            loop: false,
          }}
          className="w-full"
        >
          <CarouselContent>
            {sortedScans.map((scan) => (
              <CarouselItem key={scan.id} className="md:basis-1/2 lg:basis-1/3">
                <Card 
                  className={`h-full border-2 transition-colors ${
                    selectedScan?.id === scan.id 
                      ? 'border-medical dark:border-medical-light' 
                      : 'border-transparent'
                  }`}
                  onClick={() => handleScanClick(scan)}
                >
                  <CardHeader className="p-3 pb-0">
                    <div className="flex justify-between items-start">
                      <Badge variant={scan.status === 'Completed' ? 'default' : 'outline'}>
                        {scan.type}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <ZoomIn className="mr-2 h-4 w-4" />
                            <span>{t('common.view')}</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="mr-2 h-4 w-4" />
                            <span>{t('common.download')}</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Pencil className="mr-2 h-4 w-4" />
                            <span>{t('common.edit')}</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="p-3">
                    <Dialog>
                      <DialogTrigger asChild>
                        <div className="cursor-pointer">
                          <AspectRatio ratio={1} className="bg-muted rounded-md overflow-hidden">
                            <img
                              src={scan.imageUrl}
                              alt={`${scan.type} - ${scan.bodyPart}`}
                              className="object-cover w-full h-full"
                            />
                          </AspectRatio>
                        </div>
                      </DialogTrigger>
                      <DialogContent className="max-w-3xl">
                        <DialogHeader>
                          <DialogTitle>{scan.type} - {format(new Date(scan.date), 'PPP')}</DialogTitle>
                          <DialogDescription>{scan.bodyPart} - {scan.result.diagnosis}</DialogDescription>
                        </DialogHeader>
                        <div className="mt-4">
                          <img
                            src={scan.imageUrl}
                            alt={`${scan.type} - ${scan.bodyPart}`}
                            className="w-full rounded-md"
                          />
                        </div>
                        <div className="mt-4 space-y-2">
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div className="text-muted-foreground">{t('scans.tumorType')}:</div>
                            <div>{scan.result.tumorType || '-'}</div>
                            <div className="text-muted-foreground">{t('scans.tumorSize')}:</div>
                            <div>{scan.result.tumorSize || '-'}</div>
                            <div className="text-muted-foreground">{t('scans.tumorLocation')}:</div>
                            <div>{scan.result.tumorLocation || '-'}</div>
                            <div className="text-muted-foreground">{t('scans.malignant')}:</div>
                            <div>{scan.result.malignant ? t('common.yes') : t('common.no')}</div>
                          </div>
                          <div className="pt-2 border-t">
                            <div className="text-muted-foreground text-sm mb-1">{t('scans.notes')}:</div>
                            <p className="text-sm">{scan.result.notes}</p>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                  <CardFooter className="p-3 pt-0 flex-col items-start">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                      <Calendar className="h-3 w-3" />
                      <span>{format(new Date(scan.date), 'PPP')}</span>
                    </div>
                    <h4 className="font-medium text-sm">{scan.result.diagnosis}</h4>
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {scan.result.notes}
                    </p>
                  </CardFooter>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="mt-4 flex justify-end gap-2">
            <CarouselPrevious className="static translate-y-0" />
            <CarouselNext className="static translate-y-0" />
          </div>
        </Carousel>
      )}
    </div>
  );
};

export default ScanGallery;

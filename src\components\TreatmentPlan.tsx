import React from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Calendar, 
  Clock, 
  Pill, 
  Stethoscope, 
  FileText, 
  Scissors, 
  Radiation,
  CheckCircle,
  AlertCircle,
  Activity,
  Zap
} from 'lucide-react';
import { Treatment } from '@/data/mockPatients';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

interface TreatmentPlanProps {
  treatments: Treatment[];
}

const TreatmentPlan: React.FC<TreatmentPlanProps> = ({ treatments }) => {
  const { t } = useTranslation();
  
  // Filter active treatments
  const activeTreatments = treatments.filter(treatment => 
    treatment.status === 'Active' || treatment.status === 'Scheduled'
  );
  
  // Get the most recent active treatment
  const primaryTreatment = activeTreatments.length > 0 
    ? activeTreatments.sort((a, b) => 
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
      )[0] 
    : null;
  
  if (activeTreatments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('treatments.currentTreatmentPlan')}</CardTitle>
          <CardDescription>{t('treatments.currentTreatmentPlanDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <AlertCircle className="h-10 w-10 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">{t('treatments.noActiveTreatments')}</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full">
            <Pill className="mr-2 h-4 w-4" />
            {t('treatments.createTreatmentPlan')}
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  // Calculate treatment progress if end date exists
  const calculateProgress = (treatment: Treatment) => {
    if (!treatment.endDate) return null;
    
    const startDate = new Date(treatment.startDate).getTime();
    const endDate = new Date(treatment.endDate).getTime();
    const today = new Date().getTime();
    
    if (today >= endDate) return 100;
    if (today <= startDate) return 0;
    
    const totalDuration = endDate - startDate;
    const elapsed = today - startDate;
    return Math.round((elapsed / totalDuration) * 100);
  };
  
  // Get icon based on treatment type
  const getTreatmentIcon = (type: string) => {
    switch (type) {
      case 'Medication':
        return <Pill className="h-5 w-5 text-blue-500" />;
      case 'Surgery':
        return <Scissors className="h-5 w-5 text-red-500" />;
      case 'Radiation':
        return <Radiation className="h-5 w-5 text-amber-500" />;
      case 'Chemotherapy':
        return <Activity className="h-5 w-5 text-purple-500" />;
      default:
        return <FileText className="h-5 w-5 text-slate-500" />;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('treatments.currentTreatmentPlan')}</CardTitle>
        <CardDescription>{t('treatments.currentTreatmentPlanDescription')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Primary treatment */}
        {primaryTreatment && (
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  {getTreatmentIcon(primaryTreatment.type)}
                </div>
                <div>
                  <h3 className="font-medium">{primaryTreatment.name}</h3>
                  <p className="text-sm text-muted-foreground">{primaryTreatment.type}</p>
                </div>
              </div>
              <Badge variant={primaryTreatment.status === 'Active' ? 'default' : 'outline'}>
                {primaryTreatment.status}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{t('treatments.startDate')}:</span>
                  <span>{format(new Date(primaryTreatment.startDate), 'PPP')}</span>
                </div>
                
                {primaryTreatment.endDate && (
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{t('treatments.endDate')}:</span>
                    <span>{format(new Date(primaryTreatment.endDate), 'PPP')}</span>
                  </div>
                )}
                
                {primaryTreatment.frequency && (
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{t('treatments.frequency')}:</span>
                    <span>{primaryTreatment.frequency}</span>
                  </div>
                )}
                
                {primaryTreatment.dosage && (
                  <div className="flex items-center gap-2 text-sm">
                    <Pill className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{t('treatments.dosage')}:</span>
                    <span>{primaryTreatment.dosage}</span>
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Stethoscope className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{t('treatments.prescribedBy')}:</span>
                  <span>{primaryTreatment.doctor}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{t('treatments.effectiveness')}:</span>
                  <span>{primaryTreatment.effectiveness}</span>
                </div>
                
                {primaryTreatment.sideEffects.length > 0 && (
                  <div className="flex items-start gap-2 text-sm">
                    <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <span className="text-muted-foreground">{t('treatments.sideEffects')}:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {primaryTreatment.sideEffects.map((effect, index) => (
                          <Badge key={index} variant="outline" className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800">
                            {effect}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {primaryTreatment.endDate && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{t('treatments.progress')}</span>
                  <span>{calculateProgress(primaryTreatment)}%</span>
                </div>
                <Progress value={calculateProgress(primaryTreatment) || 0} className="h-2" />
              </div>
            )}
            
            <div className="pt-2">
              <div className="text-sm text-muted-foreground mb-1">{t('treatments.notes')}:</div>
              <p className="text-sm">{primaryTreatment.notes}</p>
            </div>
          </div>
        )}
        
        {/* Other active treatments */}
        {activeTreatments.length > 1 && (
          <>
            <Separator />
            <div>
              <h4 className="font-medium mb-3">{t('treatments.additionalTreatments')}</h4>
              <div className="space-y-3">
                {activeTreatments
                  .filter(t => t.id !== primaryTreatment?.id)
                  .map(treatment => (
                    <div key={treatment.id} className="flex items-center justify-between bg-slate-50 dark:bg-slate-800 p-3 rounded-md">
                      <div className="flex items-center gap-3">
                        <div>
                          {getTreatmentIcon(treatment.type)}
                        </div>
                        <div>
                          <div className="font-medium">{treatment.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {treatment.type} • {format(new Date(treatment.startDate), 'PP')}
                            {treatment.frequency && ` • ${treatment.frequency}`}
                          </div>
                        </div>
                      </div>
                      <Badge variant={treatment.status === 'Active' ? 'default' : 'outline'}>
                        {treatment.status}
                      </Badge>
                    </div>
                  ))
                }
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">
          <FileText className="mr-2 h-4 w-4" />
          {t('treatments.viewFullDetails')}
        </Button>
        <Button>
          <Pill className="mr-2 h-4 w-4" />
          {t('treatments.updateTreatmentPlan')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TreatmentPlan;

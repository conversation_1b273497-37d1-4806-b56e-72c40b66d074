{"deployment_id": "unet_brain_segmentation_1.0.0_20250526_011149_development_20250526_011149", "model_id": "unet_brain_segmentation_1.0.0_20250526_011149", "environment": "development", "deployed_at": "2025-05-26T01:11:49.874690", "model_metadata": {"model_id": "unet_brain_segmentation_1.0.0_20250526_011149", "model_name": "unet_brain_segmentation", "version": "1.0.0", "file_path": "mlops\\models\\unet_brain_segmentation_1.0.0_20250526_011149.h5", "file_hash": "ebd8316dc558209b30debe874f6b7c0d", "file_size": 93301080, "registered_at": "2025-05-26T01:11:49.635022", "tags": ["brain_tumor", "segmentation", "u-net", "medical_imaging", "brats2020"], "status": "registered", "metrics": {"accuracy": 0.892, "dice_coefficient": 0.847, "precision": 0.883, "recall": 0.856, "specificity": 0.945, "iou": 0.734, "validation_loss": 0.234}, "training_info": {"dataset": "BraTS 2020", "training_samples": 369, "validation_samples": 125, "test_samples": 166, "epochs": 100, "batch_size": 16, "learning_rate": 0.001, "optimizer": "<PERSON>", "loss_function": "categorical_crossentropy", "early_stopping": true, "data_augmentation": true, "training_time_hours": 24.5, "framework": "TensorFlow 2.13.0", "model_size_mb": 88.98}, "model_config": {"architecture": "U-Net", "input_shape": [128, 128, 2], "output_classes": 4, "framework": "TensorFlow"}, "dataset_info": {"name": "BraTS 2020", "size": "4.16 GB", "modalities": ["FLAIR", "T1ce"]}, "performance": {}, "deployment_history": []}, "deployment_config": {"auto_deploy": true, "health_check_enabled": true, "monitoring_enabled": true}, "status": "deployed", "health_status": "unknown"}
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { 
  Users, 
  FileText, 
  PlusCircle, 
  Upload, 
  Clock, 
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, description }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <div className="h-8 w-8 rounded-full bg-medical/10 flex items-center justify-center">
        {icon}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
    </CardContent>
  </Card>
);

interface ActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  to: string;
}

const ActionCard: React.FC<ActionCardProps> = ({ title, description, icon, to }) => (
  <Card className="overflow-hidden">
    <CardHeader className="pb-2">
      <div className="h-8 w-8 rounded-full bg-medical/10 flex items-center justify-center">
        {icon}
      </div>
      <CardTitle className="text-lg mt-2">{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardFooter className="pt-2">
      <Button asChild variant="ghost" className="p-0 h-auto font-normal text-medical">
        <Link to={to} className="flex items-center">
          <span>View</span>
          <ArrowRight className="ml-1 h-4 w-4" />
        </Link>
      </Button>
    </CardFooter>
  </Card>
);

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { userData } = useAuth();
  const isAdmin = userData?.role === 'admin';
  const displayName = userData?.displayName || '';

  // Mock data
  const stats = {
    patientCount: 128,
    scanCount: 342,
    pendingScans: 12,
    completedScans: 330,
  };

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">
            {t('dashboard.welcome')}, {displayName}
          </h1>
          <p className="text-muted-foreground">
            {new Date().toLocaleDateString(undefined, { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title={t('dashboard.patientCount')}
            value={stats.patientCount}
            icon={<Users className="h-4 w-4 text-medical" />}
          />
          <StatCard
            title={t('dashboard.scanCount')}
            value={stats.scanCount}
            icon={<FileText className="h-4 w-4 text-medical" />}
          />
          <StatCard
            title={t('dashboard.pendingScans')}
            value={stats.pendingScans}
            icon={<Clock className="h-4 w-4 text-medical" />}
          />
          <StatCard
            title={t('dashboard.completedScans')}
            value={stats.completedScans}
            icon={<CheckCircle className="h-4 w-4 text-medical" />}
          />
        </div>

        <h2 className="text-xl font-semibold mt-10 mb-4">{t('dashboard.quickActions')}</h2>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <ActionCard
            title={t('dashboard.viewAllPatients')}
            description="View and manage all patient records"
            icon={<Users className="h-4 w-4 text-medical" />}
            to="/patients"
          />
          <ActionCard
            title={t('dashboard.addNewPatient')}
            description="Register a new patient in the system"
            icon={<PlusCircle className="h-4 w-4 text-medical" />}
            to="/patients/new"
          />
          <ActionCard
            title={t('dashboard.uploadNewScan')}
            description="Upload and analyze a new MRI scan"
            icon={<Upload className="h-4 w-4 text-medical" />}
            to="/scans/new"
          />
          {isAdmin && (
            <ActionCard
              title={t('dashboard.manageUsers')}
              description="Manage user accounts and permissions"
              icon={<Users className="h-4 w-4 text-medical" />}
              to="/users"
            />
          )}
        </div>

        <h2 className="text-xl font-semibold mt-10 mb-4">{t('dashboard.recentScans')}</h2>
        
        <Card>
          <CardHeader>
            <CardTitle>{t('dashboard.recentScans')}</CardTitle>
            <CardDescription>
              Recently uploaded and analyzed scans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-6 text-muted-foreground">
              <p>No recent scans to display</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline">{t('common.view')} {t('scans.scanList')}</Button>
            <Button>{t('scans.uploadScan')}</Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;

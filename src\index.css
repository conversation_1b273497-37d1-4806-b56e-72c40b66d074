
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.85rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 199 89% 48%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', sans-serif;
  }

  /* Custom scroll bar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-medical/30 rounded-full transition-colors;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-medical/60;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/80 dark:bg-slate-800/80 backdrop-blur-md border border-white/20 dark:border-slate-700/20 shadow-md;
  }

  .neuro-button {
    @apply px-6 py-3 font-medium rounded-lg shadow-sm transition-all duration-300 transform hover:shadow-md active:scale-[0.98] disabled:opacity-70 disabled:pointer-events-none;
  }

  .neuro-button-primary {
    @apply neuro-button bg-medical dark:bg-medical-dark text-white hover:bg-medical-dark dark:hover:bg-medical;
  }

  .neuro-button-secondary {
    @apply neuro-button bg-neuro dark:bg-neuro-dark text-medical-darker dark:text-medical-light hover:bg-neuro-dark dark:hover:bg-neuro;
  }

  .neuro-button-outline {
    @apply neuro-button bg-transparent border border-medical dark:border-medical-light text-medical dark:text-medical-light hover:bg-medical/5 dark:hover:bg-medical/10;
  }

  .neuro-input {
    @apply block w-full rounded-lg border border-slate-200 dark:border-slate-700 bg-white/90 dark:bg-slate-800/90 p-3 text-slate-800 dark:text-slate-200 focus:border-medical dark:focus:border-medical-light focus:ring-2 focus:ring-medical/20 dark:focus:ring-medical-light/20 focus:outline-none transition-all duration-200;
  }

  .neuro-label {
    @apply text-sm font-medium text-slate-700 dark:text-slate-300 mb-1.5 block;
  }

  .page-transition-enter {
    @apply opacity-0;
  }

  .page-transition-enter-active {
    @apply opacity-100 transition-opacity duration-300;
  }

  .page-transition-exit {
    @apply opacity-100;
  }

  .page-transition-exit-active {
    @apply opacity-0 transition-opacity duration-300;
  }

  .section {
    @apply py-12 md:py-20;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

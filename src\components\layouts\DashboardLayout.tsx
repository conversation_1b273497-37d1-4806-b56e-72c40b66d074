import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  ChevronDown,
  ChevronRight,
  UserCircle,
  PlusCircle,
  Search,
  Pencil as PencilIcon,
  FileImage as FileImageIcon,
  Activity as ActivityIcon
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  onClick?: () => void;
  subItems?: Array<{
    to: string;
    icon: React.ReactNode;
    label: string;
    active?: boolean;
  }>;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, active, onClick, subItems }) => {
  // Check if any subItem is active to auto-expand the menu
  const hasActiveSubItem = subItems?.some(item => item.active) || false;
  const [isOpen, setIsOpen] = useState(hasActiveSubItem);
  const hasSubItems = subItems && subItems.length > 0;

  const toggleSubMenu = (e: React.MouseEvent) => {
    if (hasSubItems) {
      e.preventDefault();
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className="flex flex-col">
      <Link
        to={hasSubItems ? '#' : to}
        className={`flex items-center justify-between px-3 py-2 rounded-md transition-colors ${
          active
            ? 'bg-medical/10 text-medical'
            : 'text-slate-600 hover:bg-slate-100 dark:text-slate-300 dark:hover:bg-slate-800'
        }`}
        onClick={hasSubItems ? toggleSubMenu : onClick}
      >
        <div className="flex items-center gap-3">
          {icon}
          <span>{label}</span>
        </div>
        {hasSubItems && (
          isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
        )}
      </Link>

      {hasSubItems && isOpen && (
        <div className="ml-6 mt-1 border-l-2 border-slate-200 dark:border-slate-700 pl-2 space-y-1">
          {subItems.map((item, index) => (
            <Link
              key={index}
              to={item.to}
              className={`flex items-center gap-3 px-3 py-2 rounded-md transition-colors text-sm ${
                item.active
                  ? 'bg-medical/10 text-medical'
                  : 'text-slate-600 hover:bg-slate-100 dark:text-slate-300 dark:hover:bg-slate-800'
              }`}
            >
              {item.icon}
              <span>{item.label}</span>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const location = useLocation();

  // Get patient ID from URL if we're on a patient detail page or any sub-page
  const patientIdMatch = location.pathname.match(/\/patients\/([^/]+)(\/.*)?$/);
  const currentPatientId = patientIdMatch ? patientIdMatch[1] : null;

  // Check if we're on the patient detail page specifically
  const isPatientDetailPage = location.pathname === `/patients/${currentPatientId}`;

  const navItems = [
    {
      to: '/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
      label: t('navigation.dashboard'),
      active: location.pathname === '/dashboard',
    },
    {
      to: '/patients',
      icon: <FileText className="h-5 w-5" />,
      label: t('navigation.patients'),
      active: location.pathname.startsWith('/patients'),
      subItems: [
        {
          to: '/patients',
          icon: <Users className="h-4 w-4" />,
          label: t('patients.patientList'),
          active: location.pathname === '/patients',
        },
        {
          to: '/patients/new',
          icon: <PlusCircle className="h-4 w-4" />,
          label: t('patients.addPatient'),
          active: location.pathname === '/patients/new',
        },
        ...(currentPatientId ? [
          {
            to: `/patients/${currentPatientId}`,
            icon: <UserCircle className="h-4 w-4" />,
            label: t('patients.patientDetails'),
            active: isPatientDetailPage,
          },
          {
            to: `/patients/${currentPatientId}/edit`,
            icon: <PencilIcon className="h-4 w-4" />,
            label: t('patients.editPatient'),
            active: location.pathname === `/patients/${currentPatientId}/edit`,
          },
          {
            to: `/patients/${currentPatientId}/exam-history`,
            icon: <FileImageIcon className="h-4 w-4" />,
            label: t('scans.examHistory'),
            active: location.pathname === `/patients/${currentPatientId}/exam-history`,
          },
          {
            to: `/patients/${currentPatientId}/treatment-tracking`,
            icon: <ActivityIcon className="h-4 w-4" />,
            label: t('treatments.treatmentTracking'),
            active: location.pathname === `/patients/${currentPatientId}/treatment-tracking`,
          }
        ] : []),
      ],
    },
    {
      to: '/users',
      icon: <Users className="h-5 w-5" />,
      label: t('navigation.users'),
      active: location.pathname.startsWith('/users'),
    },
    {
      to: '/settings',
      icon: <Settings className="h-5 w-5" />,
      label: t('navigation.settings'),
      active: location.pathname === '/settings',
    },
  ];

  return (
    <div className="flex flex-1 pt-20">
      {/* Sidebar */}
      <aside className="hidden md:flex w-64 flex-col border-r bg-background">
        <nav className="flex-1 overflow-auto p-4">
          <div className="flex flex-col gap-1">
            {navItems.map((item, index) => (
              <NavItem
                key={index}
                to={item.to}
                icon={item.icon}
                label={item.label}
                active={item.active}
                subItems={item.subItems}
              />
            ))}
          </div>
        </nav>
      </aside>

      {/* Main content */}
      <main className="flex-1 overflow-auto">
        {children}
      </main>
    </div>
  );
};

export default DashboardLayout;

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MLOps Dashboard - CereBloom Classify</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .metric-label {
            font-weight: 600;
            color: #555;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .status-healthy { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #e74c3c; }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        
        .alerts-container {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .alert-high {
            background: #fdf2f2;
            border-color: #e74c3c;
            color: #c0392b;
        }
        
        .alert-medium {
            background: #fef9e7;
            border-color: #f39c12;
            color: #d68910;
        }
        
        .alert-low {
            background: #eafaf1;
            border-color: #27ae60;
            color: #229954;
        }
        
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
            margin: 10px 5px;
        }
        
        .refresh-btn:hover {
            background: #2980b9;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .models-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .model-item {
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .model-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .model-details {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .stage-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .stage-production {
            background: #27ae60;
            color: white;
        }
        
        .stage-staging {
            background: #f39c12;
            color: white;
        }
        
        .stage-development {
            background: #3498db;
            color: white;
        }
        
        .stage-none {
            background: #95a5a6;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 MLOps Dashboard</h1>
            <p>CereBloom Classify - Surveillance des Modèles de Segmentation</p>
            <button class="refresh-btn" onclick="refreshDashboard()">🔄 Actualiser</button>
            <button class="refresh-btn" onclick="exportReport()">📊 Exporter Rapport</button>
        </div>
        
        <div class="dashboard-grid">
            <!-- Santé Globale -->
            <div class="card">
                <h3>🏥 Santé Globale</h3>
                <div id="health-status" class="loading">Chargement...</div>
            </div>
            
            <!-- Modèles -->
            <div class="card">
                <h3>🤖 Modèles Enregistrés</h3>
                <div id="models-status" class="loading">Chargement...</div>
            </div>
            
            <!-- Métriques de Performance -->
            <div class="card">
                <h3>📈 Métriques (24h)</h3>
                <div id="performance-metrics" class="loading">Chargement...</div>
            </div>
            
            <!-- Alertes Actives -->
            <div class="card">
                <h3>🚨 Alertes Actives</h3>
                <div id="alerts-container" class="loading">Chargement...</div>
            </div>
        </div>
        
        <!-- Graphiques -->
        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 Temps d'Inférence</h3>
                <div class="chart-container">
                    <canvas id="inferenceChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>🎯 Distribution de Confiance</h3>
                <div class="chart-container">
                    <canvas id="confidenceChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Liste des Modèles -->
        <div class="card">
            <h3>📋 Liste des Modèles</h3>
            <div id="models-list" class="models-list loading">Chargement...</div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:8000';
        let authToken = null;
        
        // Authentification
        async function authenticate() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    return true;
                }
                return false;
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }
        
        // Requête API avec authentification
        async function apiRequest(endpoint) {
            if (!authToken) {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }
            }
            
            const response = await fetch(`${API_BASE}${endpoint}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });
            
            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status}`);
            }
            
            return response.json();
        }
        
        // Chargement de la santé globale
        async function loadHealthStatus() {
            try {
                const health = await apiRequest('/mlops/health');
                const container = document.getElementById('health-status');
                
                const status = health.overall_status || 'UNKNOWN';
                const statusClass = status === 'HEALTHY' ? 'status-healthy' : 
                                  status === 'DEGRADED' ? 'status-warning' : 'status-error';
                
                container.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">Status Global:</span>
                        <span class="metric-value ${statusClass}">${status}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Alertes Actives:</span>
                        <span class="metric-value">${health.alerts?.count || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Dernière Mise à Jour:</span>
                        <span class="metric-value">${new Date().toLocaleTimeString()}</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('health-status').innerHTML = 
                    `<div class="status-error">Erreur: ${error.message}</div>`;
            }
        }
        
        // Chargement des modèles
        async function loadModelsStatus() {
            try {
                const models = await apiRequest('/mlops/models');
                const container = document.getElementById('models-status');
                
                const modelsList = models.models || [];
                const totalModels = modelsList.length;
                
                // Comptage par stage
                const stages = {};
                modelsList.forEach(model => {
                    const stage = model.stage || 'none';
                    stages[stage] = (stages[stage] || 0) + 1;
                });
                
                let stagesHtml = '';
                Object.entries(stages).forEach(([stage, count]) => {
                    stagesHtml += `
                        <div class="metric">
                            <span class="metric-label">${stage}:</span>
                            <span class="metric-value">${count}</span>
                        </div>
                    `;
                });
                
                container.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">Total:</span>
                        <span class="metric-value">${totalModels}</span>
                    </div>
                    ${stagesHtml}
                `;
            } catch (error) {
                document.getElementById('models-status').innerHTML = 
                    `<div class="status-error">Erreur: ${error.message}</div>`;
            }
        }
        
        // Chargement des métriques
        async function loadPerformanceMetrics() {
            try {
                const metrics = await apiRequest('/mlops/monitoring/metrics?hours=24');
                const container = document.getElementById('performance-metrics');
                
                const totalPredictions = metrics.total_predictions || 0;
                const errorRate = metrics.error_rate_percent || 0;
                const avgInference = metrics.inference_time?.avg_ms || 0;
                const avgConfidence = metrics.confidence?.avg || 0;
                
                container.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">Prédictions:</span>
                        <span class="metric-value">${totalPredictions}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Taux d'Erreur:</span>
                        <span class="metric-value ${errorRate > 5 ? 'status-error' : 'status-healthy'}">${errorRate.toFixed(1)}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Temps Moyen:</span>
                        <span class="metric-value">${avgInference.toFixed(0)}ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Confiance Moy:</span>
                        <span class="metric-value">${(avgConfidence * 100).toFixed(1)}%</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('performance-metrics').innerHTML = 
                    `<div class="status-error">Erreur: ${error.message}</div>`;
            }
        }
        
        // Chargement des alertes
        async function loadAlerts() {
            try {
                const alerts = await apiRequest('/mlops/monitoring/alerts');
                const container = document.getElementById('alerts-container');
                
                const alertsList = alerts.alerts || [];
                
                if (alertsList.length === 0) {
                    container.innerHTML = '<div class="status-healthy">✅ Aucune alerte active</div>';
                    return;
                }
                
                let alertsHtml = '<div class="alerts-container">';
                alertsList.forEach(alert => {
                    const severityClass = `alert-${alert.severity?.toLowerCase() || 'low'}`;
                    alertsHtml += `
                        <div class="alert ${severityClass}">
                            <strong>${alert.type}</strong><br>
                            ${alert.message}<br>
                            <small>${new Date(alert.timestamp).toLocaleString()}</small>
                        </div>
                    `;
                });
                alertsHtml += '</div>';
                
                container.innerHTML = alertsHtml;
            } catch (error) {
                document.getElementById('alerts-container').innerHTML = 
                    `<div class="status-error">Erreur: ${error.message}</div>`;
            }
        }
        
        // Chargement de la liste des modèles
        async function loadModelsList() {
            try {
                const models = await apiRequest('/mlops/models');
                const container = document.getElementById('models-list');
                
                const modelsList = models.models || [];
                
                if (modelsList.length === 0) {
                    container.innerHTML = '<div class="status-warning">Aucun modèle enregistré</div>';
                    return;
                }
                
                let modelsHtml = '';
                modelsList.forEach(model => {
                    const stage = model.stage || 'none';
                    const stageClass = `stage-${stage}`;
                    
                    modelsHtml += `
                        <div class="model-item">
                            <div class="model-name">
                                ${model.model_name} v${model.version}
                                <span class="stage-badge ${stageClass}">${stage}</span>
                            </div>
                            <div class="model-details">
                                ID: ${model.model_id}<br>
                                Enregistré: ${new Date(model.registered_at).toLocaleString()}<br>
                                Taille: ${(model.file_size / (1024*1024)).toFixed(1)} MB<br>
                                Status: ${model.status}
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = modelsHtml;
            } catch (error) {
                document.getElementById('models-list').innerHTML = 
                    `<div class="status-error">Erreur: ${error.message}</div>`;
            }
        }
        
        // Actualisation du dashboard
        async function refreshDashboard() {
            await Promise.all([
                loadHealthStatus(),
                loadModelsStatus(),
                loadPerformanceMetrics(),
                loadAlerts(),
                loadModelsList()
            ]);
        }
        
        // Export de rapport
        async function exportReport() {
            try {
                const report = await apiRequest('/mlops/export/report?days=7');
                const blob = new Blob([JSON.stringify(report, null, 2)], {
                    type: 'application/json'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `mlops_report_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
            } catch (error) {
                alert('Erreur lors de l\'export: ' + error.message);
            }
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            refreshDashboard();
            
            // Actualisation automatique toutes les 30 secondes
            setInterval(refreshDashboard, 30000);
        });
    </script>
</body>
</html>

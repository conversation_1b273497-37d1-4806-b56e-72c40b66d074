import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  TooltipProps
} from 'recharts';
import { Scan } from '@/data/mockPatients';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, FileText, Info } from 'lucide-react';
import { ChartContainer } from '@/components/ui/chart';
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface TumorEvolutionChartProps {
  scans: Scan[];
}

interface ChartData {
  date: string;
  formattedDate: string;
  size: number;
  diagnosis: string;
}

const TumorEvolutionChart: React.FC<TumorEvolutionChartProps> = ({ scans }) => {
  const { t } = useTranslation();

  // Filter scans with tumor size and sort by date
  const validScans = useMemo(() => {
    return scans
      .filter(scan => scan.result.tumorSize)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [scans]);

  // Prepare data for the chart
  const chartData: ChartData[] = useMemo(() => {
    return validScans.map(scan => ({
      date: scan.date,
      formattedDate: format(new Date(scan.date), 'MMM d, yyyy'),
      size: parseFloat(scan.result.tumorSize?.replace('cm', '').trim() || '0'),
      diagnosis: scan.result.diagnosis
    }));
  }, [validScans]);

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as ChartData;
      return (
        <div className="bg-white dark:bg-slate-800 p-3 border rounded-md shadow-md">
          <p className="font-medium">{data.formattedDate}</p>
          <p className="text-sm text-medical">
            {t('scans.tumorSize')}: {payload[0].value} cm
          </p>
          <p className="text-xs text-muted-foreground mt-1">{data.diagnosis}</p>
        </div>
      );
    }
    return null;
  };

  if (validScans.length < 2) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('scans.tumorEvolution')}</CardTitle>
          <CardDescription>{t('scans.tumorEvolutionDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <FileText className="h-10 w-10 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">{t('scans.notEnoughDataForChart')}</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate trend
  const firstSize = chartData[0].size;
  const lastSize = chartData[chartData.length - 1].size;
  const trend = lastSize - firstSize;
  const trendPercentage = (trend / firstSize) * 100;

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{t('scans.tumorEvolution')}</CardTitle>
            <CardDescription>{t('scans.tumorEvolutionDescription')}</CardDescription>
          </div>
          <TooltipProvider>
            <UITooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t('scans.tumorSizeChartInfo')}</p>
              </TooltipContent>
            </UITooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="formattedDate" 
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                label={{ 
                  value: t('scans.tumorSizeCm'), 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { textAnchor: 'middle' }
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="size"
                name={t('scans.tumorSize')}
                stroke="#16a34a"
                activeDot={{ r: 8 }}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
            <div className="text-xs text-muted-foreground mb-1">{t('scans.initialSize')}</div>
            <div className="font-medium">{firstSize} cm</div>
            <div className="text-xs text-muted-foreground mt-1">
              {format(new Date(chartData[0].date), 'MMM d, yyyy')}
            </div>
          </div>
          
          <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
            <div className="text-xs text-muted-foreground mb-1">{t('scans.currentSize')}</div>
            <div className="font-medium">{lastSize} cm</div>
            <div className="text-xs text-muted-foreground mt-1">
              {format(new Date(chartData[chartData.length - 1].date), 'MMM d, yyyy')}
            </div>
          </div>
          
          <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
            <div className="text-xs text-muted-foreground mb-1">{t('scans.sizeChange')}</div>
            <div className={`font-medium ${trend < 0 ? 'text-green-500' : trend > 0 ? 'text-red-500' : 'text-yellow-500'}`}>
              {trend < 0 ? '↓' : trend > 0 ? '↑' : '→'} {Math.abs(trend).toFixed(1)} cm ({trendPercentage.toFixed(1)}%)
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {trend < 0 
                ? t('scans.decreaseSinceFirst') 
                : trend > 0 
                  ? t('scans.increaseSinceFirst') 
                  : t('scans.noChangeSinceFirst')}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" size="sm" className="ml-auto">
          <Download className="mr-2 h-4 w-4" />
          {t('common.exportChart')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TumorEvolutionChart;
